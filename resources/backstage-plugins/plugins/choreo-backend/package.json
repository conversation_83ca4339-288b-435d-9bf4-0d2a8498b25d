{"name": "@internal/plugin-choreo-backend", "version": "0.1.0", "license": "Apache-2.0", "private": true, "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-defaults": "^0.8.2", "@backstage/backend-plugin-api": "^1.2.1", "@backstage/catalog-client": "^1.9.1", "@backstage/config": "^1.3.2", "@backstage/errors": "^1.2.7", "@backstage/plugin-catalog-node": "^1.16.1", "@backstage/plugin-kubernetes-backend": "^0.19.5", "@backstage/plugin-kubernetes-node": "^0.2.5", "@backstage/plugin-permission-common": "^0.8.4", "@internal/plugin-openchoreo-api": "^0.1.0", "@wso2/cell-diagram": "0.2.0", "express": "^4.17.1", "express-promise-router": "^4.1.0"}, "devDependencies": {"@backstage/backend-test-utils": "^1.3.1", "@backstage/cli": "^0.31.0", "@types/express": "^4.17.6", "@types/supertest": "^2.0.12", "supertest": "^6.2.4"}, "files": ["dist"]}