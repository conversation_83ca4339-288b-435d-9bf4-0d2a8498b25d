{"name": "@internal/plugin-openchoreo-api", "version": "0.1.0", "license": "Apache-2.0", "private": true, "description": "Node.js library for the openchoreo-api plugin", "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "node-library"}, "scripts": {"build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "devDependencies": {"@backstage/cli": "^0.32.0"}, "files": ["dist"], "dependencies": {"@backstage/backend-plugin-api": "^1.4.0", "@backstage/config": "^1.3.2", "cross-fetch": "^4.1.0", "uri-template": "^2.0.0"}}