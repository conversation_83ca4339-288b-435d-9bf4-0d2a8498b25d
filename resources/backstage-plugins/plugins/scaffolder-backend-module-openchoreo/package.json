{"name": "@internal/plugin-scaffolder-backend-module-openchoreo", "version": "0.1.0", "license": "Apache-2.0", "private": true, "description": "The openchoreo module for @backstage/plugin-scaffolder-backend", "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin-module"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-plugin-api": "^1.3.0", "@backstage/plugin-scaffolder-node": "^0.8.1"}, "devDependencies": {"@backstage/cli": "^0.32.0", "@backstage/plugin-scaffolder-node-test-utils": "^0.2.1"}, "files": ["dist"]}