{"name": "@internal/plugin-catalog-backend-module-openchoreo", "version": "0.1.0", "license": "Apache-2.0", "private": true, "description": "The openchoreo backend module for the catalog plugin.", "main": "src/index.ts", "types": "src/index.ts", "publishConfig": {"access": "public", "main": "dist/index.cjs.js", "types": "dist/index.d.ts"}, "backstage": {"role": "backend-plugin-module"}, "scripts": {"start": "backstage-cli package start", "build": "backstage-cli package build", "lint": "backstage-cli package lint", "test": "backstage-cli package test", "clean": "backstage-cli package clean", "prepack": "backstage-cli package prepack", "postpack": "backstage-cli package postpack"}, "dependencies": {"@backstage/backend-plugin-api": "^1.3.0", "@backstage/catalog-client": "^1.9.1", "@backstage/catalog-model": "^1.7.3", "@backstage/config": "^1.3.2", "@backstage/plugin-catalog-node": "^1.16.3", "@backstage/plugin-permission-common": "^0.8.4", "@internal/plugin-openchoreo-api": "*"}, "devDependencies": {"@backstage/backend-test-utils": "^1.3.1", "@backstage/cli": "^0.32.0"}, "files": ["dist"], "configSchema": "config.d.ts"}