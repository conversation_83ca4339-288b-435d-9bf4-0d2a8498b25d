// Copyright 2025 The OpenChoreo Authors
// SPDX-License-Identifier: Apache-2.0

package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"gopkg.in/yaml.v3"
)

// generateRBAC generates RBAC resources for the helm chart
func (g *Generator) generateRBAC() error {
	log.Println("Generating RBAC resources...")

	// Ensure the controller directory exists
	controllerDir := g.controllerDir()
	if err := ensureDir(controllerDir); err != nil {
		return fmt.Errorf("failed to create controller directory: %w", err)
	}

	// Copy and template the controller role
	if err := g.copyControllerRole(controllerDir); err != nil {
		return fmt.Errorf("failed to copy controller role: %w", err)
	}

	return nil
}

// ClusterRole represents the structure of a Kubernetes ClusterRole
type ClusterRole struct {
	Rules []interface{} `yaml:"rules"`
}

// Template for the controller-manager-role.yaml
const controllerRoleTemplate = `# This file is auto-generated by helm-gen. DO NOT EDIT.
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "openchoreo-control-plane.name" . }}-{{ .Values.controllerManager.name }}
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
rules:
%s`

// copyControllerRole generates the controller ClusterRole using a template
func (g *Generator) copyControllerRole(controllerDir string) error {
	srcFile := filepath.Join(g.configDir, "rbac", "role.yaml")
	dstFile := filepath.Join(controllerDir, "controller-manager-role.yaml")

	// Read and parse the source role file
	content, err := os.ReadFile(srcFile)
	if err != nil {
		return fmt.Errorf("failed to read controller role: %w", err)
	}

	// Parse the YAML to extract rules
	var role ClusterRole
	if err := yaml.Unmarshal(content, &role); err != nil {
		return fmt.Errorf("failed to parse role YAML: %w", err)
	}

	// Convert rules to YAML
	rulesYAML, err := yaml.Marshal(role.Rules)
	if err != nil {
		return fmt.Errorf("failed to marshal rules: %w", err)
	}

	// Format the output using sprintf with the constant template
	output := fmt.Sprintf(controllerRoleTemplate, rulesYAML)

	// Write to file
	if err := os.WriteFile(dstFile, []byte(output), 0644); err != nil {
		return fmt.Errorf("failed to write controller role: %w", err)
	}

	log.Printf("  Generated: %s -> %s", srcFile, dstFile)
	return nil
}
