apiVersion: openchoreo.dev/v1alpha1
kind: GitCommitRequest
metadata:
  name: update-component-config
spec:
  repoURL: "https://github.com/Mirage20/openchoreo-gitops"
  branch: "controller-test"
  message: "Update component configuration for user-service"
  author:
    name: "OpenChoreo"
    email: "<EMAIL>"
  authSecretRef: "git-credentials"
  files:
    - path: "environments/dev/user-service/workload.yaml"
      content: |
        apiVersion: openchoreo.dev/v1alpha1
        kind: Workload
        metadata:
          name: user-service
          namespace: dev
        spec:
          workloadClassRef: "go-service-standard"
          image: "myregistry/user-service:v1.2.3"
          replicas: 2
    - path: "environments/dev/user-service/endpoint.yaml"
      patch: |
        [
          {
            "op": "replace",
            "path": "/spec/port",
            "value": 8080
          }
        ]

