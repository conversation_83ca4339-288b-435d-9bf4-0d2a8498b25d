apiVersion: openchoreo.dev/v1alpha1
kind: ComponentV2
metadata:
  name: email
spec:
  owner:
    projectName: gcp-microservice-demo
  type: Service

---

apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: email
spec:
  owner:
    componentName: email
    projectName: gcp-microservice-demo
  containers:
    main:
      image: us-central1-docker.pkg.dev/google-samples/microservices-demo/emailservice:v0.10.3
      env:
        - key: DISABLE_PROFILER
          value: "1"
        - key: PORT
          value: "5000"
  endpoints:
    grpc-endpoint:
      type: gRPC
      port: 5000


---
apiVersion: openchoreo.dev/v1alpha1
kind: Service
metadata:
  name: email
spec:
  owner:
    componentName: email
    projectName: gcp-microservice-demo
  workloadName: email
  overrides: {}
