apiVersion: openchoreo.dev/v1alpha1
kind: ComponentV2
metadata:
  name: checkout
spec:
  owner:
    projectName: gcp-microservice-demo
  type: Service

---

apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: checkout
spec:
  owner:
    componentName: checkout
    projectName: gcp-microservice-demo
  containers:
    main:
      image: us-central1-docker.pkg.dev/google-samples/microservices-demo/checkoutservice:v0.10.3
      env:
        - key: DISABLE_PROFILER
          value: "1"
        - key: PORT
          value: "5050"

  endpoints:
    grpc-endpoint:
      type: gRPC
      port: 5050

  connections:
    productcatalog:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: productcatalog
        endpoint: grpc-endpoint
      inject:
        env:
          - name: PRODUCT_CATALOG_SERVICE_ADDR
            value: "{{ .host }}:{{ .port }}"
    shipping:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: shipping
        endpoint: grpc-endpoint
      inject:
        env:
          - name: SHIPPING_SERVICE_ADDR
            value: "{{ .host }}:{{ .port }}"
    payment:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: payment
        endpoint: grpc-endpoint
      inject:
        env:
          - name: PAYMENT_SERVICE_ADDR
            value: "{{ .host }}:{{ .port }}"
    email:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: email
        endpoint: grpc-endpoint
      inject:
        env:
          - name: EMAIL_SERVICE_ADDR
            value: "{{ .host }}:{{ .port }}"
    currency:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: currency
        endpoint: grpc-endpoint
      inject:
        env:
          - name: CURRENCY_SERVICE_ADDR
            value: "{{ .host }}:{{ .port }}"
    cart:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: cart
        endpoint: grpc-endpoint
      inject:
        env:
          - name: CART_SERVICE_ADDR
            value: "{{ .host }}:{{ .port }}"


---
apiVersion: openchoreo.dev/v1alpha1
kind: Service
metadata:
  name: checkout
spec:
  owner:
    componentName: checkout
    projectName: gcp-microservice-demo
  workloadName: checkout
  overrides: {}
