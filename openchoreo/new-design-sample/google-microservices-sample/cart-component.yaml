apiVersion: openchoreo.dev/v1alpha1
kind: ComponentV2
metadata:
  name: cart
spec:
  owner:
    projectName: gcp-microservice-demo
  type: Service

---

apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: cart
spec:
  owner:
    componentName: cart
    projectName: gcp-microservice-demo
  containers:
    main:
      image: us-central1-docker.pkg.dev/google-samples/microservices-demo/cartservice:v0.10.3
      env:
        - key: PORT
          value: "7070"

  endpoints:
    grpc-endpoint:
      type: gRPC
      port: 7070

  connections:
    redis-cache:
      type: api
      params:
        projectName: gcp-microservice-demo
        componentName: redis
        endpoint: tcp-endpoint
      inject:
        env:
          - name: REDIS_ADDR
            value: "{{ .host }}:{{ .port }}"


---
apiVersion: openchoreo.dev/v1alpha1
kind: Service
metadata:
  name: cart
spec:
  owner:
    componentName: cart
    projectName: gcp-microservice-demo
  workloadName: cart
  overrides: {}
