apiVersion: openchoreo.dev/v1alpha1
kind: ComponentV2
metadata:
  name: currency
spec:
  owner:
    projectName: gcp-microservice-demo
  type: Service

---

apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: currency
spec:
  owner:
    componentName: currency
    projectName: gcp-microservice-demo
  containers:
    main:
      image: us-central1-docker.pkg.dev/google-samples/microservices-demo/currencyservice:v0.10.3
      env:
        - key: DISABLE_PROFILER
          value: "1"
        - key: PORT
          value: "7000"
  endpoints:
    grpc-endpoint:
      type: gRPC
      port: 7000


---
apiVersion: openchoreo.dev/v1alpha1
kind: Service
metadata:
  name: currency
spec:
  owner:
    componentName: currency
    projectName: gcp-microservice-demo
  workloadName: currency
  overrides: {}
