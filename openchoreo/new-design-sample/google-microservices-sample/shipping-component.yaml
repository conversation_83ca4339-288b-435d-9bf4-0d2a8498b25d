apiVersion: openchoreo.dev/v1alpha1
kind: ComponentV2
metadata:
  name: shipping
spec:
  owner:
    projectName: gcp-microservice-demo
  type: Service

---

apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: shipping
spec:
  owner:
    componentName: shipping
    projectName: gcp-microservice-demo
  containers:
    main:
      image: us-central1-docker.pkg.dev/google-samples/microservices-demo/shippingservice:v0.10.3
      env:
        - key: DISABLE_PROFILER
          value: "1"
        - key: PORT
          value: "50055"
  endpoints:
    grpc-endpoint:
      type: gRPC
      port: 50055


---
apiVersion: openchoreo.dev/v1alpha1
kind: Service
metadata:
  name: shipping
spec:
  owner:
    componentName: shipping
    projectName: gcp-microservice-demo
  workloadName: shipping
  overrides: { }
