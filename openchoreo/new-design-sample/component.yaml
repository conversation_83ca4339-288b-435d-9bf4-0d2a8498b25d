apiVersion: openchoreo.dev/v1alpha1
kind: ComponentV2
metadata:
  name: reading-list-service
spec:
  owner:
    projectName: default

  # Defines how to create the artifact
  #  build:
  #    className: go-1-22
  #    repository:
  #      url: https://github.com/wso2/choreo-samples
  #      ref:
  #        #        branch: main
  #        commit: 4061be9
  #    path: /go-reading-list-rest-api
  #    env:
  #      - name: CGO_ENABLED
  #        value: "0"

  # Type of the component. Examples: Service, WebApplication, ScheduleTask, ProxyApi
  type: Service

  #  settingRef:
  #    # Reference to the service settings that defines how the component is configured during deployment
  #    name: reading-list-service-workload
  #    kind: Service
  # Mutually exclusive fields that are specific to the component type

  # Defines the workload that deployed as a microservice
  # Can expose APIs from endpoints and connect to other components and consume platform resources
  # This will create Service type kind by copying the details from here.
#  service:
  #    className: go-service-standard
  #    workloadName: reading-list-service
  #    apis: { }
  # Defines the workload that deployed as a scheduled task
#  scheduleTask:
#    className: go-task-standard
#    workloadName: reading-list-service
#    cronSchedule: "0 0 * * *"  # Runs every day at midnight
  # Defines the workload that deployed as a web application
  #  webApplication:
  #    className: go-webapp-standard
  #    workloadName: reading-list-service
  # Defines a API which just proxies requests to another API (mostly external APIs like Salesforce, Stripe, etc.)
#  proxyApi: { }

---

# Defines a workload that specifies the developer contract which describes the source code including
# what configuration is needed to run, what endpoints are exposed, and how it connects to other components or platform resources.
apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: reading-list-service
spec:
  owner:
    componentName: reading-list-service
    projectName: default
  containers:
    main:
      image: ghcr.io/openchoreo/samples/greeter-service:latest
      command:
        - ./go-greeter
      args:
        - --port
        - "9090"
      env:
        - key: LOG_LEVEL
          value: info
        - key: GITHUB_REPOSITORY
          valueFrom:
            configurationGroupRef:
              key: repository
              name: github
        - key: GITHUB_TOKEN
          valueFrom:
            configurationGroupRef:
              key: pat
              name: github
  endpoints:
    rest-api:
      type: REST
      port: 9090
  connections: { } # How does this look like?

---

# Service that specify runtime configuration for the component.
# This can be either managed by the component controller or manually created by the user.
apiVersion: openchoreo.dev/v1alpha1
kind: Service
metadata:
  name: reading-list-service
spec:
  owner:
    componentName: reading-list-service
    projectName: default
  workloadName: reading-list-service
  className: go-service-standard
  overrides: { }
  apis:
    reading-list: # Should we go with map type or array type?
      type: REST
      className: rest-api-standard
      rest:
        target:
          port: 9090
          basePath: /api/v1
        operations:
          - method: GET

---
# Defines PE level configuration for the service component.
# Individual component types will have their own classes. E.g., ScheduledTaskClass, WebApplicationClass, etc.
apiVersion: openchoreo.dev/v1alpha1
kind: ServiceClass
metadata:
  name: go-service-standard
spec:
  deploymentTemplate:
    replicas: 1
    strategy:
      type: RollingUpdate
      rollingUpdate:
        maxSurge: 1
        maxUnavailable: 0
    template:
      metadata:
        labels:
          reloader.stakater.com/auto: "true"
      spec:
        containers:
          - name: main
            env:
              - name: LOG_TYPE
                value: "json"
            resources:
              requests:
                cpu: 200m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 512Mi
          - name: sidecar
            image: busybox:latest
            command: [ "/bin/sh", "-c" ]
            args:
              - |
                echo "Exposing metrics on port 12001";
                # Simulate long-running metrics server
                while true; do nc -lk -p 12001 -e echo "metrics 1"; done
            ports:
              - name: metrics
                containerPort: 12001
            resources:
              requests:
                cpu: 200m
                memory: 256Mi
              limits:
                cpu: 500m
                memory: 512Mi
            readinessProbe:
              tcpSocket:
                port: metrics
              initialDelaySeconds: 5
              periodSeconds: 10
  serviceTemplate:
    ports:
      - name: metrics-http
        port: 12001
        targetPort: 12001

---

# Environment-specific binding that connects the service to an environment.
# The Service controller will create/update this binding for the first environment (mostly development),
# and the user can manually create this for other environments to promote the service.
# This binding should be self-sufficient to generate all the necessary resources to run the service in the environment. This means,
# - It should contain a snapshot of the workload that is deployed in the environment.
# - It should contain the runtime-specific details related to the component type.
# - It can still refer to the Classes so that class changes can be applied to the runtime.
# The controller should use the Classes and other spec details to generate the necessary resources in the environment.
apiVersion: openchoreo.dev/v1alpha1
kind: ServiceBinding
metadata:
  name: reading-list-service
spec:
  owner:
    componentName: reading-list-service
    projectName: default
  environment: development
  workload:
    className: go-service-standard
    spec:
      containers:
        main:
          image: ghcr.io/openchoreo/samples/greeter-service:latest
          command:
            - ./go-greeter
          args:
            - --port
            - "9090"
          env:
            - key: LOG_LEVEL
              value: info
            - key: GITHUB_REPOSITORY
              valueFrom:
                configurationGroupRef:
                  key: repository
                  name: github
            - key: GITHUB_TOKEN
              valueFrom:
                configurationGroupRef:
                  key: pat
                  name: github
      endpoints:
        rest-api:
          type: TCP
          port: 9090
      connections: { } # How does this look like?
  apis:
    reading-list: # Should we go with map type or array type?
      type: REST
      className: rest-api-standard
      rest:
        target:
          port: 9090
          basePath: /api/v1
        operations:
          - method: GET
            path: /reading-list
            description: Get all books in the reading list
            scopes: [ "books:read" ]
            exposeLevels: [ "Organization", "Public" ]
          - method: POST
            path: /reading-list
            description: Add a book to the reading list
            scopes: [ "books:write" ]
            exposeLevels: [ "Organization", "Public" ]
          - method: GET
            path: /reading-list/{id}
            description: Get a book from the reading list by ID
            scopes: [ "books:read" ]
            exposeLevels: [ "Organization", "Public" ]
          - method: PUT
            path: /reading-list/{id}
            description: Update a book in the reading list by ID
            scopes: [ "books:write" ]
            exposeLevels: [ "Organization", "Public" ]
          - method: DELETE
            path: /reading-list/{id}
            description: Delete a book from the reading list by ID
            scopes: [ "books:write" ]
            exposeLevels: [ "Organization", "Public" ]
          - method: DELETE
            path: /reading-list
            description: Delete all books from the reading list
            scopes: [ "books:write" ]
            exposeLevels: [ "Organization" ]
      grpc: { }  # Placeholder for gRPC configuration if needed
---


# Scheduled task workload that is created based on the component configuration.
apiVersion: openchoreo.dev/v1alpha1
kind: ScheduledTask
metadata:
  name: reading-list-service-workload
spec:
  owner:
    componentName: reading-list-service
    projectName: default
  className: go-task-standard
  workloadName: reading-list-service-workload
  override:
    cronSchedule: "0 0 * * *"  # Runs every day at midnight
