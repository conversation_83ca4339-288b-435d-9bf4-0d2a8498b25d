# OpenChoreo Development Setup

This guide provides step-by-step instructions for setting up a local development environment for OpenChoreo using Kind (Kubernetes in Docker).

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) v20.10+ installed and running
- [Kind](https://kind.sigs.k8s.io/docs/user/quick-start/#installation) v0.20+ installed
- [kubectl](https://kubernetes.io/docs/tasks/tools/) v1.32+ installed
- [Helm](https://helm.sh/docs/intro/install/) v3.12+ installed

### Verify Prerequisites

Before proceeding, verify that all tools are installed and meet the minimum version requirements:

```bash
# Check Docker (should be v20.10+)
docker --version

# Check Kind (should be v0.20+) 
kind --version

# Check kubectl (should be v1.32+)
kubectl version --client

# Check Helm (should be v3.12+)
helm version --short
```

Make sure <PERSON><PERSON> is running:

```bash
docker info
```

## Step-by-Step Setup

### 1. Clean Up Existing Clusters (if any)

First, check if there are any existing Kind clusters:newgrp docker

```bash
kind get clusters
```

If you see any existing clusters (like "choreo" or "openchoreo"), delete them:

```bash
kind delete cluster --name <cluster-name>
```

**Important**: If you're reinstalling after a previous installation, also clean up any stale cert-manager leader election leases from kube-system to avoid delays:

```bash
kubectl delete lease cert-manager-controller -n kube-system --ignore-not-found
kubectl delete lease cert-manager-cainjector-leader-election -n kube-system --ignore-not-found
```

### 2. Create OpenChoreo Kind Cluster

Create a new Kind cluster using the provided configuration:

```bash
kind create cluster --config install/kind/kind-config.yaml
```

This will:
- Create a cluster named "openchoreo" 
- Set up control plane and worker nodes
- Configure the worker node with OpenChoreo-specific labels
- Set kubectl context to "kind-openchoreo"

### 3. Install Cilium CNI

Install Cilium as the Container Network Interface (CNI). This will create the `cilium` namespace automatically:

```bash
helm install cilium install/helm/cilium/ --create-namespace --namespace cilium --wait
```

Wait for Cilium pods to be ready:

```bash
kubectl wait --for=condition=Ready pod -l k8s-app=cilium -n cilium --timeout=300s
```

Verify that the nodes are now Ready:

```bash
kubectl get nodes
```

You should see both nodes in `Ready` status.

### 4. Build OpenChoreo Images

Build the OpenChoreo Docker images locally:

```bash
# Build controller and API images
make docker.build.controller
make docker.build.openchoreo-api
```

This will create `ghcr.io/openchoreo/controller:latest-dev` and `ghcr.io/openchoreo/openchoreo-api:latest-dev` images locally.

### 5. Load OpenChoreo Images

```bash
# Load images into Kind cluster
kind load docker-image ghcr.io/openchoreo/controller:latest-dev --name openchoreo
kind load docker-image ghcr.io/openchoreo/openchoreo-api:latest-dev --name openchoreo
```

### 6. Install OpenChoreo Control Plane

Install the OpenChoreo control plane using the local Helm chart. This will create the `openchoreo-control-plane` namespace automatically:

```bash
helm install control-plane install/helm/openchoreo-control-plane/ \
  --create-namespace --namespace openchoreo-control-plane \
  --timeout=10m \
  --set controllerManager.manager.imagePullPolicy=IfNotPresent \
  --set openchoreoApi.imagePullPolicy=IfNotPresent
```

Wait for the installation to complete and verify all pods are running:

```bash
kubectl get pods -n openchoreo-control-plane
```

You should see pods for:
- `controller-manager` (Running)
- `api-server` (Running) 
- `cert-manager-*` (3 pods, all Running)

### 7. Install OpenChoreo Data Plane

Install the OpenChoreo data plane using the local Helm chart. This will create the `openchoreo-data-plane` namespace automatically:

```bash
helm install data-plane install/helm/openchoreo-data-plane/ \
  --create-namespace --namespace openchoreo-data-plane \
  --timeout=10m \
  --set cert-manager.enabled=false \
  --set cert-manager.crds.enabled=false
```

Note: We disable cert-manager since it's already installed by the control plane.

Wait for dataplane components to be ready:

```bash
kubectl get pods -n openchoreo-data-plane
```

You should see pods for:
- `vault-0` (Running)
- `secrets-store-csi-driver-*` (Running on each node)
- `gateway-*` (Running)
- `registry-*` (Running)
- `redis-*` (Running)
- `envoy-gateway-*` (Running)
- `envoy-ratelimit-*` (Running)
- `fluent-bit-*` (Running)

### 8. Install OpenChoreo Build Plane (Optional)

Install the OpenChoreo build plane for CI/CD capabilities using Argo Workflows. This will create the `openchoreo-build-plane` namespace automatically:

```bash
helm install build-plane install/helm/openchoreo-build-plane/ --create-namespace --namespace openchoreo-build-plane --timeout=10m
```

Wait for the build plane components to be ready:

```bash
kubectl get pods -n openchoreo-build-plane
```

You should see pods for:
- `argo-workflow-controller-*` (Running)

Verify that the build workflow templates are available:

```bash
kubectl get clusterworkflowtemplate
```

You should see:
- `default-buildpack-template`
- `default-docker-template`

#### Configure BuildPlane

Register the build plane with the control plane by running:

```bash
bash install/add-build-plane.sh
```

This script will:
- Detect single-cluster mode automatically
- Extract cluster credentials from your kubeconfig
- Create a BuildPlane resource in the default namespace
- Configure the build plane connection to the control plane

Verify the BuildPlane was created:

```bash
kubectl get buildplane -n default
```

### 9. Install OpenChoreo Observability Plane (Optional)

Install the OpenChoreo observability plane for monitoring and logging capabilities.

First, build and load the observer image:

```bash
# Build observer image
make docker.build.observer

# Load observer image into Kind cluster
kind load docker-image ghcr.io/openchoreo/observer:latest-dev --name openchoreo
```

Then install the observability plane. This will create the `openchoreo-observability-plane` namespace automatically:

```bash
helm install observability-plane install/helm/openchoreo-observability-plane/ \
  --create-namespace --namespace openchoreo-observability-plane \
  --timeout=10m \
  --set observer.image.pullPolicy=IfNotPresent
```

Wait for the observability plane components to be ready:

```bash
kubectl get pods -n openchoreo-observability-plane
```

You should see pods for:
- `opensearch-0` (Running) - Log storage backend
- `opensearch-dashboard-*` (Running) - Visualization dashboard
- `observer-*` (Running) - Log processing service

Note: The OpenSearch pod may take several minutes to start as it downloads a large image (~1GB).

Verify all pods are ready:

```bash
kubectl wait --for=condition=Ready pod --all -n openchoreo-observability-plane --timeout=600s
```

Verify FluentBit is sending logs to OpenSearch:

```bash
# Check if kubernetes indices are being created
kubectl exec -n openchoreo-observability-plane opensearch-0 -- curl -s "http://localhost:9200/_cat/indices?v" | grep kubernetes

# Check recent log count
kubectl exec -n openchoreo-observability-plane opensearch-0 -- curl -s "http://localhost:9200/kubernetes-*/_count" | jq '.count'
```

If the indices exist and the count is greater than 0, FluentBit is successfully collecting and storing logs.

To access the OpenSearch Dashboard:

```bash
# Port forward the dashboard
kubectl port-forward -n openchoreo-observability-plane svc/opensearch-dashboard 5601:5601

# Access in browser at http://localhost:5601
```

### 10. Configure DataPlane

Register the data plane with the control plane by running:

```bash
bash install/add-default-dataplane.sh
```

This script will:
- Detect single-cluster mode automatically
- Extract cluster credentials from your kubeconfig
- Create a DataPlane resource in the default namespace
- Configure the registry and gateway endpoints

Verify the DataPlane was created:

```bash
kubectl get dataplane -n default
```

### 11. Verify OpenChoreo Installation

Check that default OpenChoreo resources were created:

```bash
# Check default organization and project
kubectl get organizations,projects,environments -A

# Check default platform classes
kubectl get serviceclass,apiclass -n default

# Check all OpenChoreo CRDs
kubectl get crds | grep openchoreo

# Check gateway resources
kubectl get gateway,httproute -n openchoreo-data-plane
```

### 12. Verify Complete Setup

Check that all components are running:

```bash
# Check cluster info
kubectl cluster-info --context kind-openchoreo

# Check control plane pods
kubectl get pods -n openchoreo-control-plane

# Check data plane pods
kubectl get pods -n openchoreo-data-plane

# Check build plane pods (if installed)
kubectl get pods -n openchoreo-build-plane

# Check observability plane pods (if installed)
kubectl get pods -n openchoreo-observability-plane

# Check Cilium pods
kubectl get pods -n cilium

# Check nodes (should be Ready)
kubectl get nodes
```

## Optional: Local Development Mode

If you want to run the controller manager locally for development:

### Scale Down the Deployed Controller

First, scale down the existing controller manager deployment:

```bash
kubectl -n openchoreo-control-plane scale deployment controller-manager --replicas=0
```

### Update DataPlane Configuration

Update the DataPlane resource to use your local machine's API server URL:

```bash
kubectl get dataplane default -n default -o json | \
  jq --arg url "$(kubectl config view --raw -o jsonpath="{.clusters[?(@.name=='kind-openchoreo')].cluster.server}")" \
  '.spec.kubernetesCluster.credentials.apiServerURL = $url' | \
  kubectl apply -f -
```

### Run Controller Manager Locally

Now you can run the controller manager locally:

```bash
make go.run.manager ENABLE_WEBHOOKS=false
```

To restore the deployment when done with local development:

```bash
kubectl -n openchoreo-control-plane scale deployment controller-manager --replicas=1
```

## Sample Application Deployment

Now that OpenChoreo is fully installed, let's test it with a sample application.

### Deploy the Secure Service Sample

Deploy the secure-service-with-jwt sample to verify your setup:

```bash
# Deploy the greeter service (ComponentV2, Workload, Service)
kubectl apply -f new-design-sample/use-prebuilt-image/secure-service-with-jwt/greeter-service-with-jwt.yaml
```

### Test the Service

1. Wait for the service to be ready (this may take 1-2 minutes):

```bash
# Check that all resources are created
kubectl get componentv2,workload,service,api -A

# Wait for HTTP routes to be created
kubectl get httproute -A
```

2. Test the greeter service from inside the cluster:

```bash
kubectl run test-curl --image=curlimages/curl --rm -i --restart=Never -- \
  curl -v -k https://gateway-external.openchoreo-data-plane.svc.cluster.local/default/greeter-service/greeter/greet \
  -H "Host: development.choreoapis.localhost"
```

You should receive a successful response:
```
Hello, Stranger!
```

### Clean Up Sample

To remove the sample application:

```bash
# Remove the greeter service
kubectl delete -f new-design-sample/use-prebuilt-image/secure-service-with-jwt/greeter-service-with-jwt.yaml
```

## Next Steps

After completing this setup and testing the sample, you can:

1. Deploy additional sample applications from `new-design-sample/use-prebuilt-image/`
2. Test the v1alpha1 API migration
3. Develop and test new OpenChoreo features

## Troubleshooting

### OpenChoreo Control Plane Issues

If the OpenChoreo control plane installation fails:

```bash
# Check Helm release status
helm status control-plane -n openchoreo-control-plane

# Check pod logs for controller manager
kubectl logs -n openchoreo-control-plane -l app.kubernetes.io/component=controller-manager

# Check pod logs for API server
kubectl logs -n openchoreo-control-plane -l app.kubernetes.io/component=api-server

# Check if images are properly loaded
kubectl describe pod -n openchoreo-control-plane <pod-name>
```

If webhook certificates are missing:

```bash
# Check certificate resources
kubectl get certificates,issuers -n openchoreo-control-plane

# Check webhook secrets
kubectl get secrets -n openchoreo-control-plane | grep webhook
```

### OpenChoreo Data Plane Issues

If the dataplane installation hangs or fails:

```bash
# Check Helm release status
helm status data-plane -n openchoreo-data-plane

# Check vault status
kubectl exec -n openchoreo-data-plane vault-0 -- vault status

# Check post-install hook jobs
kubectl get jobs -n openchoreo-data-plane

# Check gateway status
kubectl describe gateway -n openchoreo-data-plane
```

Common issues:
- **Vault readiness job failing**: The job may have incorrect label selectors. Check with `kubectl logs -n openchoreo-data-plane <job-pod-name>`
- **Rate limiter CrashLoopBackOff**: Usually due to missing Redis configuration
- **Gateway not programmed**: Check Envoy Gateway controller logs

### Cilium Installation Issues

If Cilium pods are not starting properly:

```bash
# Check pod logs
kubectl logs -n cilium -l k8s-app=cilium

# Check Cilium operator logs
kubectl logs -n cilium -l io.cilium/app=operator
```

### Context Issues

If kubectl context is not set correctly:

```bash
# List available contexts
kubectl config get-contexts

# Set context manually
kubectl config use-context kind-openchoreo
```

### Version Compatibility Issues

If you encounter unexpected errors, verify that your tool versions meet the minimum requirements:

```bash
# Check all versions at once
echo "=== Docker ==="
docker --version
echo "=== Kind ==="
kind --version  
echo "=== kubectl ==="
kubectl version --client
echo "=== Helm ==="
helm version --short
```

**Minimum required versions:**
- Docker: v20.10+
- Kind: v0.20+
- kubectl: v1.32+
- Helm: v3.12+

If your versions are older, please upgrade before proceeding.

## Clean Up

To completely remove the development environment:

```bash
# Delete the Kind cluster
kind delete cluster --name openchoreo

# Remove kubectl context (optional)
kubectl config delete-context kind-openchoreo

# Clean up cert-manager leader election leases (for future reinstalls)
kubectl delete lease cert-manager-controller -n kube-system --ignore-not-found
kubectl delete lease cert-manager-cainjector-leader-election -n kube-system --ignore-not-found
```
