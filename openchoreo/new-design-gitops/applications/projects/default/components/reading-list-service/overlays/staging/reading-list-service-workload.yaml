apiVersion: openchoreo.dev/v1alpha1
kind: Workload
metadata:
  name: reading-list-service
  namespace: default
  annotations:
    choreo.dev/generated-by: componentv2-controller
    choreo.dev/component: reading-list-service
spec:
  classRef: go-service-standard
  environment: staging
  image: "ghcr.io/openchoreo/samples/greeter-service:v1.2.0"  # Specific version for staging
  env:
    - name: DATABASE_URL
      valueFrom:
        secretKeyRef:
          name: reading-list-db-credentials
          key: url
    - name: LOG_LEVEL
      value: "info"
