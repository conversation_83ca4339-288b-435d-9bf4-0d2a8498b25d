apiVersion: openchoreo.dev/v1alpha1
kind: EndpointV2
metadata:
  name: reading-list-service-api
  namespace: default
  annotations:
    choreo.dev/generated-by: componentv2-controller
    choreo.dev/component: reading-list-service
spec:
  classRef: rest-api-standard
  environment: development
  workloadRef: reading-list-service
  port: 8080
  path: "/api/v1"
  expose:
    - project
    - organization
