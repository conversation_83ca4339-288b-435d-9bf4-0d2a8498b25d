apiVersion: openchoreo.dev/v1alpha1
kind: Environment
metadata:
  annotations:
    openchoreo.dev/description: Development
    openchoreo.dev/display-name: Development
  labels:
    openchoreo.dev/name: development
    openchoreo.dev/organization: default
  name: development
  namespace: default
spec:
  dataPlaneRef: default
  gateway:
    dnsPrefix: dev
    security:
      remoteJwks:
        uri: ""
