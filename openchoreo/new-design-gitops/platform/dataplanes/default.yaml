apiVersion: openchoreo.dev/v1alpha1
kind: DataPlane
metadata:
  labels:
    openchoreo.dev/name: default
    openchoreo.dev/organization: default
  name: default
  namespace: default
spec:
  registry:
    unauthenticated:
      - "docker.io"
      - "ghcr.io"
      - "quay.io"
    imagePushSecrets: []
  kubernetesCluster:
    name: default-dataplane
    credentials:
      apiServerURL: "https://kubernetes.default.svc"
      caCert: ""
      clientCert: ""
      clientKey: ""
  gateway:
    publicVirtualHost: "api.choreo.dev"
    organizationVirtualHost: "api-org.choreo.dev"
