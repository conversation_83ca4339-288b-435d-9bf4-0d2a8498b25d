apiVersion: v1
kind: Service
metadata:
  name: opensearch
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-observability-plane.componentLabels" (dict "context" . "component" "opensearch") | nindent 4 }}
spec:
  ports:
  - port: {{ .Values.opensearch.service.httpPort }}
    name: http
    targetPort: http
    {{- if .Values.opensearch.service.nodePort }}
    nodePort: {{ .Values.opensearch.service.nodePort }}
    {{- end }}
  - port: {{ .Values.opensearch.service.transportPort }}
    name: transport
    targetPort: transport
  selector:
    {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "opensearch") | nindent 4 }}
  type: {{ .Values.opensearch.service.type }}
