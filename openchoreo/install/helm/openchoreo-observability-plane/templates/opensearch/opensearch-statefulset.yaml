apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: opensearch
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-observability-plane.componentLabels" (dict "context" . "component" "opensearch") | nindent 4 }}
spec:
  serviceName: opensearch
  replicas: {{ .Values.opensearch.replicas }}
  selector:
    matchLabels:
      {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "opensearch") | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "opensearch") | nindent 8 }}
    spec:
      containers:
      - name: opensearch
        image: "{{ .Values.opensearch.image.repository }}:{{ .Values.opensearch.image.tag }}"
        imagePullPolicy: {{ .Values.opensearch.image.pullPolicy }}
        env:
        - name: cluster.name
          value: {{ .Values.opensearch.config.clusterName }}
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.type
          value: {{ .Values.opensearch.config.discoveryType }}
        - name: OPENSEARCH_JAVA_OPTS
          value: {{ .Values.opensearch.config.javaOpts | quote }}
        - name: bootstrap.memory_lock
          value: {{ .Values.opensearch.config.memoryLock | quote }}
        - name: DISABLE_SECURITY_PLUGIN
          value: {{ .Values.opensearch.config.disableSecurity | quote }}
        ports:
        - containerPort: {{ .Values.opensearch.service.httpPort }}
          name: http
        - containerPort: {{ .Values.opensearch.service.transportPort }}
          name: transport
        volumeMounts:
        - name: data
          mountPath: /usr/share/opensearch/data
        resources:
          {{- toYaml .Values.opensearch.resources | nindent 10 }}
  {{- if .Values.opensearch.persistence.enabled }}
  volumeClaimTemplates:
  - metadata:
      name: data
    spec:
      accessModes:
      - {{ .Values.opensearch.persistence.accessMode }}
      {{- if .Values.opensearch.persistence.storageClass }}
      storageClassName: {{ .Values.opensearch.persistence.storageClass }}
      {{- end }}
      resources:
        requests:
          storage: {{ .Values.opensearch.persistence.size }}
  {{- end }}
