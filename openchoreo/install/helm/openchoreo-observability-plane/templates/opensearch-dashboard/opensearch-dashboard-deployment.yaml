apiVersion: apps/v1
kind: Deployment
metadata:
  name: opensearch-dashboard
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-observability-plane.componentLabels" (dict "context" . "component" "opensearch-dashboard") | nindent 4 }}
spec:
  replicas: {{ .Values.opensearchDashboard.replicas }}
  selector:
    matchLabels:
      {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "opensearch-dashboard") | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "opensearch-dashboard") | nindent 8 }}
    spec:
      containers:
      - name: opensearch-dashboard
        image: "{{ .Values.opensearchDashboard.image.repository }}:{{ .Values.opensearchDashboard.image.tag }}"
        imagePullPolicy: {{ .Values.opensearchDashboard.image.pullPolicy }}
        env:
        - name: OPENSEARCH_HOSTS
          value: '["http://opensearch:{{ .Values.opensearch.service.httpPort }}"]'
        - name: DISABLE_SECURITY_DASHBOARDS_PLUGIN
          value: {{ .Values.opensearchDashboard.config.disableSecurity | quote }}
        ports:
        - containerPort: {{ .Values.opensearchDashboard.service.port }}
          name: http
