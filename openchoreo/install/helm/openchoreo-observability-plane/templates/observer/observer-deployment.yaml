apiVersion: apps/v1
kind: Deployment
metadata:
  name: observer
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-observability-plane.componentLabels" (dict "context" . "component" "observer") | nindent 4 }}
spec:
  replicas: {{ .Values.observer.replicas | default 1 }}
  selector:
    matchLabels:
      {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "observer") | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "observer") | nindent 8 }}
    spec:
      serviceAccountName: observer
      securityContext:
        runAsNonRoot: true
        runAsUser: 65532
        runAsGroup: 65532
        fsGroup: 65532
      initContainers:
      - name: wait-for-opensearch
        image: curlimages/curl:latest
        command:
        - /bin/sh
        - -c
        - |
          echo "Waiting for OpenSearch to be ready..."
          OPENSEARCH_URL="http://opensearch:{{ .Values.opensearch.service.httpPort }}"
          echo "Checking OpenSearch at: $OPENSEARCH_URL"
          until curl -s "$OPENSEARCH_URL/_cluster/health" | grep -q '"status":"green\|yellow"'; do
            echo "OpenSearch not ready, waiting 10 seconds..."
            sleep 10
          done
          echo "OpenSearch is ready!"
        securityContext:
          runAsNonRoot: true
          runAsUser: 65532
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
      containers:
      - name: observer
        image: "{{ if .Values.observer.image }}{{ .Values.observer.image.repository | default "ghcr.io/openchoreo/observer" }}:{{ .Values.observer.image.tag | default "latest-dev" }}{{ else }}ghcr.io/openchoreo/observer:latest-dev{{ end }}"
        imagePullPolicy: {{ if .Values.observer.image }}{{ .Values.observer.image.pullPolicy | default "IfNotPresent" }}{{ else }}IfNotPresent{{ end }}
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        env:
        - name: PORT
          value: "8080"
        - name: LOG_LEVEL
          value: {{ if .Values.observer }}{{ .Values.observer.logLevel | default "info" | quote }}{{ else }}"info"{{ end }}
        - name: OPENSEARCH_ADDRESS
          value: "http://opensearch:{{ .Values.opensearch.service.httpPort }}"
        - name: OPENSEARCH_USERNAME
          valueFrom:
            secretKeyRef:
              name: observer-opensearch
              key: username
        - name: OPENSEARCH_PASSWORD
          valueFrom:
            secretKeyRef:
              name: observer-opensearch
              key: password
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        resources:
          {{- if .Values.observer.resources }}
          {{- toYaml .Values.observer.resources | nindent 10 }}
          {{- else }}
          limits:
            cpu: 500m
            memory: 256Mi
          requests:
            cpu: 100m
            memory: 128Mi
          {{- end }}
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65532
