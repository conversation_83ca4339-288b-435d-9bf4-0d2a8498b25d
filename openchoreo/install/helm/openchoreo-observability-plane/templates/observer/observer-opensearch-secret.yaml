apiVersion: v1
kind: Secret
metadata:
  name: observer-opensearch
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-observability-plane.componentLabels" (dict "context" . "component" "observer") | nindent 4 }}
type: Opaque
data:
  username: {{ .Values.opensearch.authentication.basicAuth.username | b64enc }}
  password: {{ .Values.opensearch.authentication.basicAuth.password | b64enc }}
