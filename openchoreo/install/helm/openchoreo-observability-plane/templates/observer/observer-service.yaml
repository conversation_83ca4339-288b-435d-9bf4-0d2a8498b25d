apiVersion: v1
kind: Service
metadata:
  name: observer
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-observability-plane.componentLabels" (dict "context" . "component" "observer") | nindent 4 }}
spec:
  type: {{ if .Values.observer.service }}{{ .Values.observer.service.type | default "ClusterIP" }}{{ else }}ClusterIP{{ end }}
  ports:
  - port: {{ if .Values.observer.service }}{{ .Values.observer.service.port | default 8080 }}{{ else }}8080{{ end }}
    targetPort: http
    protocol: TCP
    name: http
  selector:
    {{- include "openchoreo-observability-plane.componentSelectorLabels" (dict "context" . "component" "observer") | nindent 4 }}
