apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: gateway-internal
spec:
  gatewayClassName: gateway
  infrastructure:
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: gateway-internal
  listeners:
    - name: https
      protocol: HTTPS
      port: 443
      allowedRoutes:
        namespaces:
          from: All
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: envoy-gateway-tls-secret
            namespace: {{ $.Values.namespace | default $.Release.Namespace }}
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: gateway-internal
  namespace: {{ $.Values.namespace | default $.Release.Namespace }}
spec:
  provider:
    type: Kubernetes
    kubernetes:
      envoyDeployment:
        name: gateway-internal
      envoyService:
        name: gateway-internal
