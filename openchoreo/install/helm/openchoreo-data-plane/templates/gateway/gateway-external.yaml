apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: gateway-external
spec:
  gatewayClassName: gateway
  infrastructure:
    parametersRef:
      group: gateway.envoyproxy.io
      kind: EnvoyProxy
      name: gateway-external
  listeners:
    - name: https
      protocol: HTTPS
      port: 443
      allowedRoutes:
        namespaces:
          from: All
      tls:
        mode: Terminate
        certificateRefs:
          - kind: Secret
            name: envoy-gateway-tls-secret
            namespace: {{ $.Values.namespace | default $.Release.Namespace }}
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: EnvoyProxy
metadata:
  name: gateway-external
  namespace: {{ $.Values.namespace | default $.Release.Namespace }}
spec:
  provider:
    type: Kubernetes
    kubernetes:
      envoyDeployment:
        name: gateway-external
      envoyService:
        name: gateway-external
