apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: self-signed-issuer
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
    labels:
    {{- include "openchoreo-data-plane.labels" . | nindent 4 }}
spec:
  selfSigned: {}
---
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: selfsigned-cert
  namespace: {{ .Values.namespace | default .Release.Namespace }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "2"
    labels:
    {{- include "openchoreo-data-plane.labels" . | nindent 4 }}
spec:
  secretName: envoy-gateway-tls-secret
  issuerRef:
    name: self-signed-issuer
    kind: ClusterIssuer
  dnsNames:
    - choreo.localhost
    - '*.choreo.localhost'
