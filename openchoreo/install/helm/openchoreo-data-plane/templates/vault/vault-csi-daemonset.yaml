apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    app.kubernetes.io/name:  hashicorp-vault-csi-provider
  name:  hashicorp-vault-csi-provider
  namespace:  {{ .Release.Namespace }}
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/name:  hashicorp-vault-csi-provider
  template:
    metadata:
      labels:
        app.kubernetes.io/name:  hashicorp-vault-csi-provider
    spec:
      serviceAccountName: default
      tolerations:
      containers:
        - name: provider-vault-installer
          image: hashicorp/vault-csi-provider:1.5.0
          imagePullPolicy: Always
          args:
            - -endpoint=/provider/vault.sock
            - -log-level=info
          resources:
            requests:
              cpu: 50m
              memory: 100Mi
            limits:
              cpu: 50m
              memory: 100Mi
          volumeMounts:
            - name: providervol
              mountPath: "/provider"
          livenessProbe:
            httpGet:
              path: "/health/ready"
              port: 8080
              scheme: "HTTP"
            failureThreshold: 2
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
          readinessProbe:
            httpGet:
              path: "/health/ready"
              port: 8080
              scheme: "HTTP"
            failureThreshold: 2
            initialDelaySeconds: 5
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 3
      volumes:
        - name: providervol
          hostPath:
            path: "/etc/kubernetes/secrets-store-csi-providers"
      nodeSelector:
        kubernetes.io/os: linux

