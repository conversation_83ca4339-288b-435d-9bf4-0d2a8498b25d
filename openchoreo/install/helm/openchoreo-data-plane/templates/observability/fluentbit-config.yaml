{{- if .Values.observability.logging.publishers.fluentbit.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: {{ $.Values.namespace | default $.Release.Namespace }}
  labels:
    {{- include "openchoreo-data-plane.labels" . | nindent 4 }}
    app.kubernetes.io/component: fluent-bit
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         {{ .Values.fluentBit.config.service.flush }}
        Log_Level     {{ .Values.fluentBit.config.service.logLevel }}
        Daemon        {{ .Values.fluentBit.config.service.daemon }}
        Parsers_File  parsers.conf

    [INPUT]
        Name              {{ .Values.fluentBit.config.input.name }}
        Tag               {{ .Values.fluentBit.config.input.tag }}
        Path              {{ .Values.fluentBit.config.input.path }}
        Exclude_Path      {{ .Values.fluentBit.config.input.excludePath }}
        Parser            {{ .Values.fluentBit.config.input.parser }}
        Inotify_Watcher   {{ if .Values.fluentBit.config.input.inotifyWatcher }}On{{ else }}Off{{ end }}
        DB                {{ .Values.fluentBit.config.input.db }}
        Mem_Buf_Limit     {{ .Values.fluentBit.config.input.memBufLimit }}
        Skip_Long_Lines   {{ if .Values.fluentBit.config.input.skipLongLines }}On{{ else }}Off{{ end }}
        Refresh_Interval  {{ .Values.fluentBit.config.input.refreshInterval }}

    [FILTER]
        Name                {{ .Values.fluentBit.config.filter.name }}
        Match               {{ .Values.fluentBit.config.filter.match }}
        Kube_URL            {{ .Values.fluentBit.config.filter.kubeURL }}
        Kube_CA_File        {{ .Values.fluentBit.config.filter.kubeCAFile }}
        Kube_Token_File     {{ .Values.fluentBit.config.filter.kubeTokenFile }}
        Kube_Tag_Prefix     {{ .Values.fluentBit.config.filter.kubeTagPrefix }}
        K8S-Logging.Parser  {{ if .Values.fluentBit.config.filter.k8sLoggingParser }}On{{ else }}Off{{ end }}
        K8S-Logging.Exclude {{ if .Values.fluentBit.config.filter.k8sLoggingExclude }}On{{ else }}Off{{ end }}

    [OUTPUT]
        Name            {{ .Values.fluentBit.config.output.name }}
        Match           {{ .Values.fluentBit.config.output.match }}
        Host            {{ .Values.fluentBit.config.opensearch.host }}
        Port            {{ .Values.fluentBit.config.opensearch.port }}
        HTTP_User       {{ .Values.fluentBit.config.opensearch.authentication.basicauth.username }}
        HTTP_Passwd     {{ .Values.fluentBit.config.opensearch.authentication.basicauth.password }}
        tls             {{ if .Values.fluentBit.config.opensearch.tls }}On{{ else }}Off{{ end }}
        tls.verify      {{ if .Values.fluentBit.config.opensearch.tlsVerify }}On{{ else }}Off{{ end }}
        Index           {{ .Values.fluentBit.config.output.index }}
        Type            {{ .Values.fluentBit.config.output.type }}
        Logstash_Format {{ if .Values.fluentBit.config.output.logstashFormat }}On{{ else }}Off{{ end }}
        Logstash_Prefix {{ .Values.fluentBit.config.output.logstashPrefix }}
        Time_Key        {{ .Values.fluentBit.config.output.timeKey }}
        Trace_Error     {{ if .Values.fluentBit.config.output.traceError }}On{{ else }}Off{{ end }}
        Suppress_Type_Name {{ if .Values.fluentBit.config.output.suppressTypeName }}On{{ else }}Off{{ end }}

  parsers.conf: |
    [PARSER]
        Name   {{ .Values.fluentBit.config.parser.name }}
        Format {{ .Values.fluentBit.config.parser.format }}
        Time_Key {{ .Values.fluentBit.config.parser.timeKey }}
        Time_Format {{ .Values.fluentBit.config.parser.timeFormat }}
        Time_Keep {{ if .Values.fluentBit.config.parser.timeKeep }}On{{ else }}Off{{ end }}
{{- end }}
