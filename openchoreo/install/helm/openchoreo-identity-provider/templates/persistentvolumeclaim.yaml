{{- if .Values.persistence.database.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "openchoreo-identity-provider.fullname" . }}-database
  labels:
    {{- include "openchoreo-identity-provider.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.persistence.database.accessMode }}
  storageClassName: manual
  resources:
    requests:
      storage: {{ .Values.persistence.database.size }}
{{- end }}
