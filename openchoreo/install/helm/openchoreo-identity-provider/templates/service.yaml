apiVersion: v1
kind: Service
metadata:
  name: {{ include "openchoreo-identity-provider.fullname" . }}
  labels:
    {{- include "openchoreo-identity-provider.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.targetPort }}
      {{- if and (eq .Values.service.type "NodePort") .Values.service.nodePort }}
      nodePort: {{ .Values.service.nodePort }}
      {{- end }}
      protocol: TCP
      name: http
  selector:
    {{- include "openchoreo-identity-provider.selectorLabels" . | nindent 4 }}
