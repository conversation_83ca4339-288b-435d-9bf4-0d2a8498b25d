# Default values for openchoreo-identity-provider.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

image:
  repository: ghcr.io/asgardeo/thunder
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "0.4.0"

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""


podAnnotations: {}
podLabels: {}

podSecurityContext:
  fsGroup: 1000

securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  runAsNonRoot: false
  runAsUser: 0

service:
  type: NodePort
  port: 8090
  targetPort: 8090
  nodePort: 30090


resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 50m
    memory: 64Mi


# Additional volumes on the output Deployment definition.
volumes: []
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: []
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

# Application configuration
config:
  server:
    hostname: "0.0.0.0"
    port: 8090
    httpOnly: true # ToDO: disable this when using HTTPS
  
  gateClient:
    hostname: "localhost"
    port: 9090
    scheme: "http"
    loginPath: "/login"
    errorPath: "/error"
  
  database:
    identity:
      type: "sqlite"
      path: "repository/database/thunderdb.db"
      options: "_journal_mode=WAL&_busy_timeout=5000"
    runtime:
      type: "sqlite"
      path: "repository/database/runtimedb.db"
      options: "_journal_mode=WAL&_busy_timeout=5000"
  
  oauth:
    jwt:
      issuer: "https://openchoreo.example.com"
      validityPeriod: 3600
    refreshToken:
      renewOnGrant: false
      validityPeriod: 86400
  
  flow:
    graphDirectory: "repository/resources/graphs/"
    authn:
      defaultFlow: "auth_flow_config_basic"
  
  security:
    certFile: "repository/resources/security/server.cert"
    keyFile: "repository/resources/security/server.key"

# Persistence for sqlite database
persistence:
  database:
    enabled: true
    # existingClaim: ""
    storageClass: "manual"
    accessMode: ReadWriteMany
    size: 1Gi

# Default application creation
# This is just a sample application that will be used for testing purposes.
defaultApplication:
  enabled: true
  name: "DefaultApplication"
  description: "Sample Application for testing"
  callbackUrl: "https://example.com/callback"
  clientId: "openchoreo-default-client"
  clientSecret: "openchoreo-default-secret"
