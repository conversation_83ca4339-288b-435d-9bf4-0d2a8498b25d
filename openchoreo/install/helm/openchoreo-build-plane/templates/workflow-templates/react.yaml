{{ if .Values.global.defaultResources.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: ClusterWorkflowTemplate
metadata:
  name: react
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
  labels:
    {{- include "openchoreo-build-plane.labels" . | nindent 4 }}
spec:
  entrypoint: build-workflow
  arguments:
    parameters:
      - name: node-version
  templates:
    - name: build-workflow
      steps:
        - - name: clone-step
            template: clone-step
        - - name: build-step
            template: build-step
            arguments:
              parameters:
                - name: git-revision
                  value: '{{ "{{" }}steps.clone-step.outputs.parameters.git-revision{{ "}}" }}'
        - - name: push-step
            template: push-step
            arguments:
              parameters:
                - name: git-revision
                  value: '{{ "{{" }}steps.clone-step.outputs.parameters.git-revision{{ "}}" }}'

    - name: clone-step
      outputs:
        parameters:
          - name: git-revision
            valueFrom:
              path: /tmp/git-revision.txt
      container:
        args:
          - |-
            set -e

            BRANCH={{ "{{" }}workflow.parameters.branch{{ "}}" }}
            REPO={{ "{{" }}workflow.parameters.git-repo{{ "}}" }}
            COMMIT={{ "{{" }}workflow.parameters.commit{{ "}}" }}

            if [[ -n "$COMMIT" ]]; then
                echo "Cloning specific commit: $COMMIT"
                git clone --no-checkout --depth 1 "$REPO" /mnt/vol/source
                cd /mnt/vol/source
                git config --global advice.detachedHead false
                git fetch --depth 1 origin "$COMMIT"
                git checkout "$COMMIT"
                echo -n "$COMMIT" | cut -c1-8 > /tmp/git-revision.txt
            else
                echo "Cloning branch: $BRANCH with latest commit"
                git clone --single-branch --branch $BRANCH --depth 1 "$REPO" /mnt/vol/source
                cd /mnt/vol/source
                COMMIT_SHA=$(git rev-parse HEAD)
                echo -n "$COMMIT_SHA" | cut -c1-8 > /tmp/git-revision.txt
            fi
        command:
          - sh
          - -c
        image: alpine/git
        name: ""
        volumeMounts:
          - mountPath: /mnt/vol
            name: workspace
    - name: build-step
      inputs:
        parameters:
          - name: git-revision
      container:
        args:
          - |-
            set -e

            WORKDIR=/mnt/vol/source
            CACHE_DIR=/shared/podman/cache

            IMAGE="{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-{{ "{{" }}inputs.parameters.git-revision{{ "}}" }}"
            APP_PATH="{{ "{{" }}workflow.parameters.app-path{{ "}}" }}"

            NODE_VERSION="{{ "{{" }}workflow.parameters.node-version{{ "}}" }}"

            #####################################################################
            # 1. Build React: Custom Dockerfile + Nginx
            #####################################################################
            cat > "$APP_PATH/Dockerfile" <<'EOF'
            FROM node:$NODE_VERSION-alpine as builder
            RUN npm install -g pnpm
            WORKDIR /app
            COPY . .
            RUN if [ -f "package-lock.json" ]; then npm ci; \
                elif [ -f "yarn.lock" ]; then yarn install --frozen-lockfile; \
                elif [ -f "pnpm-lock.yaml" ]; then pnpm install --frozen-lockfile; \
                else echo "No lock file found" && exit 1; fi
            RUN npm run build || yarn run build || pnpm run build

            FROM nginx:alpine
            COPY --from=builder /app/default.conf /etc/nginx/conf.d/default.conf
            COPY --from=builder /app/build /usr/share/nginx/html/
            EOF

            cat > "$APP_PATH/default.conf" <<'EOF'
            server {
              listen 80;
              location / {
                root   /usr/share/nginx/html;
                index  index.html;
                try_files $uri /index.html;
              }
            }
            EOF

            podman build -t "$IMAGE" -f "$APP_PATH/Dockerfile" "$APP_PATH"
            podman save -o /mnt/vol/app-image.tar "$IMAGE"
        command:
          - sh
          - -c
        image: ghcr.io/openchoreo/podman-runner:v1.0
        securityContext:
          privileged: true
        volumeMounts:
          - mountPath: /mnt/vol
            name: workspace
          - mountPath: /shared/podman/cache
            name: podman-cache
    - name: push-step
      inputs:
        parameters:
          - name: git-revision
      outputs:
        parameters:
          - name: image
            valueFrom:
              path: /tmp/image.txt
      container:
        args:
          - |-
            set -e
            GIT_REVISION={{ "{{" }}inputs.parameters.git-revision{{ "}}" }}
            mkdir -p /etc/containers
            cat <<EOF > /etc/containers/storage.conf
            [storage]
            driver = "overlay"
            runroot = "/run/containers/storage"
            graphroot = "/var/lib/containers/storage"
            [storage.options.overlay]
            mount_program = "/usr/bin/fuse-overlayfs"
            EOF

            podman load -i /mnt/vol/app-image.tar
            podman tag {{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION {{ .Values.global.defaultResources.registryEndpoint }}/{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION
            podman push --tls-verify=false {{ .Values.global.defaultResources.registryEndpoint }}/{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION

            echo -n "{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION" > /tmp/image.txt
        command:
          - sh
          - -c
        image: ghcr.io/openchoreo/podman-runner:v1.0
        securityContext:
          privileged: true
        volumeMounts:
          - mountPath: /mnt/vol
            name: workspace

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: openchoreo.dev/noderole
                operator: In
                values:
                  - workflow-runner
  ttlStrategy:
    secondsAfterFailure: 3600
    secondsAfterSuccess: 3600
  volumeClaimTemplates:
    - metadata:
        creationTimestamp: null
        name: workspace
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 2Gi
  volumes:
    - hostPath:
        path: /shared/podman/cache
        type: DirectoryOrCreate
      name: podman-cache
{{ end }}
