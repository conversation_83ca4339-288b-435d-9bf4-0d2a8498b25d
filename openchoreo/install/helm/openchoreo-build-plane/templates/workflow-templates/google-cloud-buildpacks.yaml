{{ if .Values.global.defaultResources.enabled }}
apiVersion: argoproj.io/v1alpha1
kind: ClusterWorkflowTemplate
metadata:
  name: google-cloud-buildpacks
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "1"
  labels:
    {{- include "openchoreo-build-plane.labels" . | nindent 4 }}
spec:
  entrypoint: build-workflow
  templates:
    - name: build-workflow
      steps:
        - - name: clone-step
            template: clone-step
        - - name: build-step
            template: build-step
            arguments:
              parameters:
                - name: git-revision
                  value: '{{ "{{" }}steps.clone-step.outputs.parameters.git-revision{{ "}}" }}'
        - - name: push-step
            template: push-step
            arguments:
              parameters:
                - name: git-revision
                  value: '{{ "{{" }}steps.clone-step.outputs.parameters.git-revision{{ "}}" }}'

    - name: clone-step
      outputs:
        parameters:
          - name: git-revision
            valueFrom:
              path: /tmp/git-revision.txt
      container:
        args:
          - |-
            set -e

            BRANCH={{ "{{" }}workflow.parameters.branch{{ "}}" }}
            REPO={{ "{{" }}workflow.parameters.git-repo{{ "}}" }}
            COMMIT={{ "{{" }}workflow.parameters.commit{{ "}}" }}

            if [[ -n "$COMMIT" ]]; then
                echo "Cloning specific commit: $COMMIT"
                git clone --no-checkout --depth 1 "$REPO" /mnt/vol/source
                cd /mnt/vol/source
                git config --global advice.detachedHead false
                git fetch --depth 1 origin "$COMMIT"
                git checkout "$COMMIT"
                echo -n "$COMMIT" | cut -c1-8 > /tmp/git-revision.txt
            else
                echo "Cloning branch: $BRANCH with latest commit"
                git clone --single-branch --branch $BRANCH --depth 1 "$REPO" /mnt/vol/source
                cd /mnt/vol/source
                COMMIT_SHA=$(git rev-parse HEAD)
                echo -n "$COMMIT_SHA" | cut -c1-8 > /tmp/git-revision.txt
            fi
        command:
          - sh
          - -c
        image: alpine/git
        name: ""
        volumeMounts:
          - mountPath: /mnt/vol
            name: workspace
    - name: build-step
      inputs:
        parameters:
          - name: git-revision
      container:
        args:
          - |-
            set -e

            WORKDIR=/mnt/vol/source
            CACHE_DIR=/shared/podman/cache

            IMAGE="{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-{{ "{{" }}inputs.parameters.git-revision{{ "}}" }}"
            APP_PATH="{{ "{{" }}workflow.parameters.app-path{{ "}}" }}"

            BUILDER="gcr.io/buildpacks/builder@sha256:5977b4bd47d3e9ff729eefe9eb99d321d4bba7aa3b14986323133f40b622aef1"
            RUN_IMG="gcr.io/buildpacks/google-22/run:latest"

            #####################################################################
            # 1. Podman daemon + storage.conf
            #####################################################################
            mkdir -p /etc/containers
            cat > /etc/containers/storage.conf <<EOF
            [storage]
            driver = "overlay"
            runroot = "/run/containers/storage"
            graphroot = "/var/lib/containers/storage"
            [storage.options.overlay]
            mount_program = "/usr/bin/fuse-overlayfs"
            EOF

            podman system service --time=0 &
            until podman info --format '{{ "{{" }}.Host.RemoteSocket.Exists{{ "}}" }}' 2>/dev/null | grep -q true; do sleep 1; done

            #####################################################################
            # 2. Cache builder/run images
            #####################################################################
            ensure_cached () {
              local image=$1 tar=$2
              if [[ ! -f "$tar" ]]; then
                podman pull "$image"
                podman save -o "$tar" "$image"
              else
                podman load -i "$tar" 2>/dev/null || {
                  podman pull "$image"
                  podman save -o "$tar" "$image"
                }
              fi
            }

            #####################################################################
            # 3. Build with Google Buildpacks
            #####################################################################
            ensure_cached "$BUILDER" "$CACHE_DIR/google-builder.tar"
            ensure_cached "$RUN_IMG" "$CACHE_DIR/google-run.tar"

            /usr/local/bin/pack build "$IMAGE" \
              --builder "$BUILDER" \
              --docker-host inherit \
              --path "$WORKDIR/$APP_PATH" \
              --pull-policy if-not-present

            podman save -o /mnt/vol/app-image.tar "$IMAGE"
        command:
          - sh
          - -c
        image: ghcr.io/openchoreo/podman-runner:v1.0
        securityContext:
          privileged: true
        volumeMounts:
          - mountPath: /mnt/vol
            name: workspace
          - mountPath: /shared/podman/cache
            name: podman-cache
    - name: push-step
      inputs:
        parameters:
          - name: git-revision
      outputs:
        parameters:
          - name: image
            valueFrom:
              path: /tmp/image.txt
      container:
        args:
          - |-
            set -e
            GIT_REVISION={{ "{{" }}inputs.parameters.git-revision{{ "}}" }}
            mkdir -p /etc/containers
            cat <<EOF > /etc/containers/storage.conf
            [storage]
            driver = "overlay"
            runroot = "/run/containers/storage"
            graphroot = "/var/lib/containers/storage"
            [storage.options.overlay]
            mount_program = "/usr/bin/fuse-overlayfs"
            EOF

            podman load -i /mnt/vol/app-image.tar
            podman tag {{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION {{ .Values.global.defaultResources.registryEndpoint }}/{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION
            podman push --tls-verify=false {{ .Values.global.defaultResources.registryEndpoint }}/{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION

            echo -n "{{ .Values.global.defaultResources.registryEndpoint }}/{{ "{{" }}workflow.parameters.image-name{{ "}}" }}:{{ "{{" }}workflow.parameters.image-tag{{ "}}" }}-$GIT_REVISION" > /tmp/image.txt
        command:
          - sh
          - -c
        image: ghcr.io/openchoreo/podman-runner:v1.0
        securityContext:
          privileged: true
        volumeMounts:
          - mountPath: /mnt/vol
            name: workspace

  affinity:
    nodeAffinity:
      requiredDuringSchedulingIgnoredDuringExecution:
        nodeSelectorTerms:
          - matchExpressions:
              - key: openchoreo.dev/noderole
                operator: In
                values:
                  - workflow-runner
  ttlStrategy:
    secondsAfterFailure: 3600
    secondsAfterSuccess: 3600
  volumeClaimTemplates:
    - metadata:
        creationTimestamp: null
        name: workspace
      spec:
        accessModes:
          - ReadWriteOnce
        resources:
          requests:
            storage: 2Gi
  volumes:
    - hostPath:
        path: /shared/podman/cache
        type: DirectoryOrCreate
      name: podman-cache
{{ end }}
