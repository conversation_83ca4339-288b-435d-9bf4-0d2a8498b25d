apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: webapplicationclasses.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: WebApplicationClass
    listKind: WebApplicationClassList
    plural: webapplicationclasses
    singular: webapplicationclass
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: WebApplicationClass is the Schema for the webapplicationclasses
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: WebApplicationClassSpec defines the desired state of WebApplicationClass.
            properties:
              deploymentTemplate:
                x-kubernetes-preserve-unknown-fields: true
              serviceTemplate:
                x-kubernetes-preserve-unknown-fields: true
            type: object
          status:
            description: WebApplicationClassStatus defines the observed state of WebApplicationClass.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
