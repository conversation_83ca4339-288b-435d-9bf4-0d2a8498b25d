apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: webapplicationbindings.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: WebApplicationBinding
    listKind: WebApplicationBindingList
    plural: webapplicationbindings
    singular: webapplicationbinding
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: WebApplicationBinding is the Schema for the webapplicationbindings
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: WebApplicationBindingSpec defines the desired state of WebApplicationBinding.
            properties:
              className:
                description: ClassName is the name of the web application class that
                  provides the web application-specific deployment configuration.
                type: string
              environment:
                description: Environment is the target environment for this binding
                minLength: 1
                type: string
              overrides:
                additionalProperties:
                  type: boolean
                description: Overrides contains web application-specific overrides
                  for this binding
                type: object
              owner:
                description: Owner defines the component and project that owns this
                  web application binding
                properties:
                  componentName:
                    minLength: 1
                    type: string
                  projectName:
                    minLength: 1
                    type: string
                required:
                - componentName
                - projectName
                type: object
              workloadSpec:
                description: WorkloadSpec contains the copied workload specification
                  for this environment-specific binding
                properties:
                  connections:
                    additionalProperties:
                      description: WorkloadConnection represents an internal API connection
                      properties:
                        inject:
                          description: Inject defines how connection details are injected
                            into the workload
                          properties:
                            env:
                              description: Environment variables to inject
                              items:
                                description: WorkloadConnectionEnvVar defines an environment
                                  variable injection
                                properties:
                                  name:
                                    description: Environment variable name
                                    type: string
                                  value:
                                    description: Template value using connection properties
                                      (e.g., "{{ .url }}")
                                    type: string
                                required:
                                - name
                                - value
                                type: object
                              type: array
                          required:
                          - env
                          type: object
                        params:
                          additionalProperties:
                            type: string
                          description: Parameters for connection configuration (dynamic
                            key-value pairs)
                          type: object
                        type:
                          description: Type of connection - only "api" for now
                          enum:
                          - api
                          type: string
                      required:
                      - inject
                      - type
                      type: object
                    description: |-
                      Connections define how this workload consumes internal and external resources.
                      The key is the connection name, and the value is the connection specification.
                    type: object
                  containers:
                    additionalProperties:
                      description: Container represents a single container in the
                        workload.
                      properties:
                        args:
                          items:
                            type: string
                          type: array
                        command:
                          description: Container entrypoint & args.
                          items:
                            type: string
                          type: array
                        env:
                          description: Explicit environment variables.
                          items:
                            description: EnvVar represents an environment variable
                              present in the container.
                            properties:
                              key:
                                description: The environment variable key.
                                type: string
                              value:
                                description: |-
                                  The literal value of the environment variable.
                                  Mutually exclusive with valueFrom.
                                type: string
                              valueFrom:
                                description: |-
                                  Extract the environment variable value from another resource.
                                  Mutually exclusive with value.
                                properties:
                                  configurationGroupRef:
                                    description: Reference to a configuration group.
                                    properties:
                                      key:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - key
                                    - name
                                    type: object
                                  secretRef:
                                    description: Reference to a secret resource.
                                    properties:
                                      key:
                                        type: string
                                      name:
                                        type: string
                                    required:
                                    - key
                                    - name
                                    type: object
                                type: object
                            required:
                            - key
                            type: object
                          type: array
                        image:
                          description: OCI image to run (digest or tag).
                          minLength: 1
                          type: string
                      required:
                      - image
                      type: object
                    description: |-
                      Containers define the container specifications for this workload.
                      The key is the container name, and the value is the container specification.
                    type: object
                  endpoints:
                    additionalProperties:
                      description: WorkloadEndpoint represents a simple network endpoint
                        for basic exposure.
                      properties:
                        port:
                          description: Port number for the endpoint.
                          format: int32
                          maximum: 65535
                          minimum: 1
                          type: integer
                        schema:
                          description: |-
                            Optional schema for the endpoint.
                            This can be used to define the actual API definition of the endpoint that is exposed by the workload.
                          properties:
                            content:
                              type: string
                            type:
                              type: string
                          type: object
                        type:
                          description: Type indicates the protocol/technology of the
                            endpoint (HTTP, REST, gRPC, GraphQL, Websocket, TCP, UDP).
                          enum:
                          - HTTP
                          - REST
                          - gRPC
                          - GraphQL
                          - Websocket
                          - TCP
                          - UDP
                          type: string
                      required:
                      - port
                      - type
                      type: object
                    description: |-
                      Endpoints define simple network endpoints for basic port exposure.
                      The key is the endpoint name, and the value is the endpoint specification.
                    type: object
                type: object
            required:
            - className
            - environment
            - owner
            - workloadSpec
            type: object
          status:
            description: WebApplicationBindingStatus defines the observed state of
              WebApplicationBinding.
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of the WebApplicationBinding's current state.
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              endpoints:
                description: Endpoints contain the status of each endpoint
                items:
                  description: |-
                    EndpointStatus represents the observed state of an endpoint
                    Used by ServiceBinding, WebApplicationBinding, and other binding types
                  properties:
                    name:
                      description: Name is the endpoint identifier matching spec.endpoints
                      type: string
                    organization:
                      description: Organization contains access info for organization-level
                        visibility
                      properties:
                        basePath:
                          description: BasePath is the base URL path (for HTTP-based
                            endpoints)
                          type: string
                        host:
                          description: Host is the hostname or service name
                          type: string
                        port:
                          description: Port is the port number
                          format: int32
                          type: integer
                        scheme:
                          description: Scheme is the connection scheme (http, https,
                            grpc, tcp)
                          type: string
                        uri:
                          description: |-
                            URI is the computed URI for connecting to the endpoint
                            This field is automatically generated from host, port, scheme, and basePath
                            Examples: https://api.example.com:8080/v1, grpc://service:5050, tcp://localhost:9000
                          type: string
                      required:
                      - host
                      - port
                      type: object
                    project:
                      description: Project contains access info for project-level
                        visibility
                      properties:
                        basePath:
                          description: BasePath is the base URL path (for HTTP-based
                            endpoints)
                          type: string
                        host:
                          description: Host is the hostname or service name
                          type: string
                        port:
                          description: Port is the port number
                          format: int32
                          type: integer
                        scheme:
                          description: Scheme is the connection scheme (http, https,
                            grpc, tcp)
                          type: string
                        uri:
                          description: |-
                            URI is the computed URI for connecting to the endpoint
                            This field is automatically generated from host, port, scheme, and basePath
                            Examples: https://api.example.com:8080/v1, grpc://service:5050, tcp://localhost:9000
                          type: string
                      required:
                      - host
                      - port
                      type: object
                    public:
                      description: Public contains access info for public visibility
                      properties:
                        basePath:
                          description: BasePath is the base URL path (for HTTP-based
                            endpoints)
                          type: string
                        host:
                          description: Host is the hostname or service name
                          type: string
                        port:
                          description: Port is the port number
                          format: int32
                          type: integer
                        scheme:
                          description: Scheme is the connection scheme (http, https,
                            grpc, tcp)
                          type: string
                        uri:
                          description: |-
                            URI is the computed URI for connecting to the endpoint
                            This field is automatically generated from host, port, scheme, and basePath
                            Examples: https://api.example.com:8080/v1, grpc://service:5050, tcp://localhost:9000
                          type: string
                      required:
                      - host
                      - port
                      type: object
                    type:
                      description: Type is the endpoint type (uses EndpointType from
                        endpoint_types.go)
                      type: string
                  required:
                  - name
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
