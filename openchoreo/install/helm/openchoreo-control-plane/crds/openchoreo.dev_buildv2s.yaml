apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: buildv2s.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: BuildV2
    listKind: BuildV2List
    plural: buildv2s
    singular: buildv2
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: BuildV2 is the Schema for the buildv2s API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: BuildV2Spec defines the desired state of BuildV2.
            properties:
              owner:
                properties:
                  componentName:
                    minLength: 1
                    type: string
                  projectName:
                    minLength: 1
                    type: string
                required:
                - componentName
                - projectName
                type: object
              repository:
                description: Repository contains the source repository configuration
                properties:
                  appPath:
                    description: AppPath is the path to the application within the
                      repository
                    type: string
                  revision:
                    description: Revision specifies the revision to build from
                    properties:
                      branch:
                        description: Branch specifies the branch to build from
                        type: string
                      commit:
                        description: Commit specifies the commit hash to build from
                        type: string
                    type: object
                  url:
                    description: URL is the repository URL
                    type: string
                required:
                - appPath
                - revision
                - url
                type: object
              templateRef:
                description: TemplateRef contains the build template reference and
                  parameters
                properties:
                  engine:
                    description: Engine specifies the build engine
                    type: string
                  name:
                    description: Name is the template name
                    type: string
                  parameters:
                    description: Parameters contains the template parameters
                    items:
                      description: Parameter defines a template parameter
                      properties:
                        name:
                          description: Name is the parameter name
                          type: string
                        value:
                          description: Value is the parameter value
                          type: string
                      required:
                      - name
                      - value
                      type: object
                    type: array
                required:
                - name
                type: object
            required:
            - owner
            - repository
            - templateRef
            type: object
          status:
            description: BuildV2Status defines the observed state of BuildV2.
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of the build's current state
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              imageStatus:
                description: ImageStatus contains information about the built image
                properties:
                  image:
                    type: string
                required:
                - image
                type: object
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
