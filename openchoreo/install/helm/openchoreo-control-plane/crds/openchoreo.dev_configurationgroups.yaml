apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: configurationgroups.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    categories:
    - choreo
    - all
    kind: ConfigurationGroup
    listKind: ConfigurationGroupList
    plural: configurationgroups
    shortNames:
    - configgrp
    singular: configurationgroup
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .metadata.annotations.core\.choreo\.dev/display-name
      name: DisplayName
      type: string
    - jsonPath: .metadata.labels.core\.choreo\.dev/organization
      name: Organization
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ConfigurationGroup is the Schema for the configurationgroups
          API
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ConfigurationGroupSpec defines the desired state of ConfigurationGroup
            properties:
              configurations:
                description: Configuration parameters of the configuration group.
                items:
                  description: ConfigurationGroupConfiguration defines a configuration
                    parameter
                  properties:
                    key:
                      description: Key of the configuration parameter.
                      type: string
                    values:
                      description: |-
                        List of values for the configuration parameter.
                        These values can be applicable either to a specific environment or an environment group.
                        The value for each specified key may be either a config or a secret. These can be mixed across environments.
                        e.g. use a config value for dev and a secret for prod.
                      items:
                        description: ConfigurationValue defines the value of a configuration
                          parameter
                        properties:
                          environment:
                            description: |-
                              Reference to the environment to which this configuration parameter is applicable.

                              This field is mutually exclusive with environmentGroupRef field.
                            type: string
                          environmentGroupRef:
                            description: |-
                              Reference to the environment group to which this configuration parameter is applicable.

                              This field is mutually exclusive with environment field.
                            type: string
                          value:
                            description: |-
                              Value of the configuration parameter.

                              This field is mutually exclusive with vaultKey.
                            type: string
                          vaultKey:
                            description: |-
                              Reference to the secret vault key that contains the value for this configuration parameter.

                              This field is mutually exclusive with value.
                            type: string
                        type: object
                      type: array
                  required:
                  - key
                  - values
                  type: object
                type: array
              environmentGroups:
                description: |-
                  Environment groups that the configuration group is applicable.
                  This will be used when there are multiple similar environments to avoid repetition.
                items:
                  description: EnvironmentGroup defines a group of environments
                  properties:
                    environments:
                      description: List of environments that are part of the environment
                        group.
                      items:
                        type: string
                      type: array
                    name:
                      description: Name of the environment group.
                      type: string
                  required:
                  - environments
                  - name
                  type: object
                type: array
              scope:
                additionalProperties:
                  type: string
                default: {}
                description: Scope of the configuration group.
                type: object
            required:
            - configurations
            type: object
          status:
            description: ConfigurationGroupStatus defines the observed state of ConfigurationGroup
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of the ConfigurationGroup's state
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
