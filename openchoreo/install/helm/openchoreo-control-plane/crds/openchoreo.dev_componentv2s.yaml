apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: componentv2s.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: ComponentV2
    listKind: ComponentV2List
    plural: componentv2s
    singular: componentv2
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: ComponentV2 is the Schema for the componentv2s API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: ComponentV2Spec defines the desired state of ComponentV2.
            properties:
              build:
                description: Build defines the build configuration for the component
                properties:
                  repository:
                    description: Repository defines the source repository configuration
                      where the component code resides
                    properties:
                      appPath:
                        description: |-
                          AppPath is the path to the application within the repository
                          This is relative to the repository root. Default is "." for root directory
                        type: string
                      revision:
                        description: |-
                          Revision specifies the default revision configuration for builds
                          This can be overridden when triggering specific builds
                        properties:
                          branch:
                            description: |-
                              Branch specifies the default branch to build from
                              This will be used when no specific commit is provided for a build
                            type: string
                        required:
                        - branch
                        type: object
                      url:
                        description: |-
                          URL is the repository URL where the component source code is located
                          Example: "https://github.com/org/repo" or "**************:org/repo.git"
                        type: string
                    required:
                    - appPath
                    - revision
                    - url
                    type: object
                  templateRef:
                    description: |-
                      TemplateRef defines the build template reference and configuration
                      This references a ClusterWorkflowTemplate in the build plane
                    properties:
                      engine:
                        description: Engine specifies the build engine
                        type: string
                      name:
                        description: Name is the template name
                        type: string
                      parameters:
                        description: Parameters contains the template parameters
                        items:
                          description: Parameter defines a template parameter
                          properties:
                            name:
                              description: Name is the parameter name
                              type: string
                            value:
                              description: Value is the parameter value
                              type: string
                          required:
                          - name
                          - value
                          type: object
                        type: array
                    required:
                    - name
                    type: object
                required:
                - repository
                - templateRef
                type: object
              owner:
                description: Owner defines the ownership information for the component
                properties:
                  projectName:
                    minLength: 1
                    type: string
                required:
                - projectName
                type: object
              type:
                description: Type specifies the component type (e.g., Service, WebApplication,
                  etc.)
                type: string
            required:
            - owner
            - type
            type: object
          status:
            description: ComponentV2Status defines the observed state of ComponentV2.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
