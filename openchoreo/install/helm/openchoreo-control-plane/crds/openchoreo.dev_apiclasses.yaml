apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: apiclasses.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: APIClass
    listKind: APIClassList
    plural: apiclasses
    singular: apiclass
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: APIClass is the Schema for the apiclasses API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: APIClassSpec defines the desired state of APIClass.
            properties:
              grpcPolicy:
                description: GRPCAPIPolicy defines gRPC-specific API policies (placeholder
                  for future implementation)
                type: object
              restPolicy:
                description: RESTAPIPolicy defines REST-specific API policies
                properties:
                  defaults:
                    description: Default policies that apply to all expose levels
                    properties:
                      authentication:
                        description: Authentication and authorization configuration
                        properties:
                          apikey:
                            description: APIKeyAuthConfig defines API key authentication
                              configuration
                            properties:
                              header:
                                type: string
                              queryParam:
                                type: string
                            type: object
                          jwt:
                            description: JWTAuthConfig defines JWT authentication
                              configuration
                            properties:
                              audience:
                                items:
                                  type: string
                                type: array
                              issuer:
                                type: string
                              jwks:
                                type: string
                            required:
                            - issuer
                            - jwks
                            type: object
                          oauth2:
                            description: OAuth2AuthConfig defines OAuth2 authentication
                              configuration
                            properties:
                              scopes:
                                items:
                                  type: string
                                type: array
                              tokenUrl:
                                type: string
                            required:
                            - tokenUrl
                            type: object
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      circuitBreaker:
                        description: Circuit breaker configuration
                        properties:
                          enabled:
                            type: boolean
                          maxConnections:
                            format: int32
                            type: integer
                          maxParallelRequests:
                            format: int32
                            type: integer
                          maxParallelRetries:
                            format: int32
                            type: integer
                          maxPendingRequests:
                            format: int32
                            type: integer
                        required:
                        - enabled
                        type: object
                      cors:
                        description: CORS configuration
                        properties:
                          allowHeaders:
                            items:
                              type: string
                            type: array
                          allowMethods:
                            items:
                              type: string
                            type: array
                          allowOrigins:
                            items:
                              type: string
                            type: array
                          exposeHeaders:
                            items:
                              type: string
                            type: array
                          maxAge:
                            format: int64
                            type: integer
                        type: object
                      mediation:
                        description: Request and response mediation/transformation
                        properties:
                          requestTransformations:
                            items:
                              description: TransformationRule defines a single transformation
                                rule
                              properties:
                                action:
                                  type: string
                                fields:
                                  additionalProperties:
                                    type: string
                                  type: object
                                headerName:
                                  type: string
                                headerValue:
                                  type: string
                                headers:
                                  items:
                                    type: string
                                  type: array
                                type:
                                  type: string
                              required:
                              - action
                              - type
                              type: object
                            type: array
                          responseTransformations:
                            items:
                              description: TransformationRule defines a single transformation
                                rule
                              properties:
                                action:
                                  type: string
                                fields:
                                  additionalProperties:
                                    type: string
                                  type: object
                                headerName:
                                  type: string
                                headerValue:
                                  type: string
                                headers:
                                  items:
                                    type: string
                                  type: array
                                type:
                                  type: string
                              required:
                              - action
                              - type
                              type: object
                            type: array
                        type: object
                      rateLimit:
                        description: Rate limiting configuration
                        properties:
                          requests:
                            format: int64
                            type: integer
                          window:
                            type: string
                        required:
                        - requests
                        - window
                        type: object
                      security:
                        description: Security policies
                        properties:
                          allowedIPs:
                            items:
                              type: string
                            type: array
                          blockedIPs:
                            items:
                              type: string
                            type: array
                          minTLSVersion:
                            type: string
                          requireTLS:
                            type: boolean
                        type: object
                    type: object
                  organization:
                    description: Override policies for organization expose level
                    properties:
                      authentication:
                        description: Authentication and authorization configuration
                        properties:
                          apikey:
                            description: APIKeyAuthConfig defines API key authentication
                              configuration
                            properties:
                              header:
                                type: string
                              queryParam:
                                type: string
                            type: object
                          jwt:
                            description: JWTAuthConfig defines JWT authentication
                              configuration
                            properties:
                              audience:
                                items:
                                  type: string
                                type: array
                              issuer:
                                type: string
                              jwks:
                                type: string
                            required:
                            - issuer
                            - jwks
                            type: object
                          oauth2:
                            description: OAuth2AuthConfig defines OAuth2 authentication
                              configuration
                            properties:
                              scopes:
                                items:
                                  type: string
                                type: array
                              tokenUrl:
                                type: string
                            required:
                            - tokenUrl
                            type: object
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      circuitBreaker:
                        description: Circuit breaker configuration
                        properties:
                          enabled:
                            type: boolean
                          maxConnections:
                            format: int32
                            type: integer
                          maxParallelRequests:
                            format: int32
                            type: integer
                          maxParallelRetries:
                            format: int32
                            type: integer
                          maxPendingRequests:
                            format: int32
                            type: integer
                        required:
                        - enabled
                        type: object
                      cors:
                        description: CORS configuration
                        properties:
                          allowHeaders:
                            items:
                              type: string
                            type: array
                          allowMethods:
                            items:
                              type: string
                            type: array
                          allowOrigins:
                            items:
                              type: string
                            type: array
                          exposeHeaders:
                            items:
                              type: string
                            type: array
                          maxAge:
                            format: int64
                            type: integer
                        type: object
                      mediation:
                        description: Request and response mediation/transformation
                        properties:
                          requestTransformations:
                            items:
                              description: TransformationRule defines a single transformation
                                rule
                              properties:
                                action:
                                  type: string
                                fields:
                                  additionalProperties:
                                    type: string
                                  type: object
                                headerName:
                                  type: string
                                headerValue:
                                  type: string
                                headers:
                                  items:
                                    type: string
                                  type: array
                                type:
                                  type: string
                              required:
                              - action
                              - type
                              type: object
                            type: array
                          responseTransformations:
                            items:
                              description: TransformationRule defines a single transformation
                                rule
                              properties:
                                action:
                                  type: string
                                fields:
                                  additionalProperties:
                                    type: string
                                  type: object
                                headerName:
                                  type: string
                                headerValue:
                                  type: string
                                headers:
                                  items:
                                    type: string
                                  type: array
                                type:
                                  type: string
                              required:
                              - action
                              - type
                              type: object
                            type: array
                        type: object
                      rateLimit:
                        description: Rate limiting configuration
                        properties:
                          requests:
                            format: int64
                            type: integer
                          window:
                            type: string
                        required:
                        - requests
                        - window
                        type: object
                      security:
                        description: Security policies
                        properties:
                          allowedIPs:
                            items:
                              type: string
                            type: array
                          blockedIPs:
                            items:
                              type: string
                            type: array
                          minTLSVersion:
                            type: string
                          requireTLS:
                            type: boolean
                        type: object
                    type: object
                  public:
                    description: Override policies for public expose level
                    properties:
                      authentication:
                        description: Authentication and authorization configuration
                        properties:
                          apikey:
                            description: APIKeyAuthConfig defines API key authentication
                              configuration
                            properties:
                              header:
                                type: string
                              queryParam:
                                type: string
                            type: object
                          jwt:
                            description: JWTAuthConfig defines JWT authentication
                              configuration
                            properties:
                              audience:
                                items:
                                  type: string
                                type: array
                              issuer:
                                type: string
                              jwks:
                                type: string
                            required:
                            - issuer
                            - jwks
                            type: object
                          oauth2:
                            description: OAuth2AuthConfig defines OAuth2 authentication
                              configuration
                            properties:
                              scopes:
                                items:
                                  type: string
                                type: array
                              tokenUrl:
                                type: string
                            required:
                            - tokenUrl
                            type: object
                          type:
                            type: string
                        required:
                        - type
                        type: object
                      circuitBreaker:
                        description: Circuit breaker configuration
                        properties:
                          enabled:
                            type: boolean
                          maxConnections:
                            format: int32
                            type: integer
                          maxParallelRequests:
                            format: int32
                            type: integer
                          maxParallelRetries:
                            format: int32
                            type: integer
                          maxPendingRequests:
                            format: int32
                            type: integer
                        required:
                        - enabled
                        type: object
                      cors:
                        description: CORS configuration
                        properties:
                          allowHeaders:
                            items:
                              type: string
                            type: array
                          allowMethods:
                            items:
                              type: string
                            type: array
                          allowOrigins:
                            items:
                              type: string
                            type: array
                          exposeHeaders:
                            items:
                              type: string
                            type: array
                          maxAge:
                            format: int64
                            type: integer
                        type: object
                      mediation:
                        description: Request and response mediation/transformation
                        properties:
                          requestTransformations:
                            items:
                              description: TransformationRule defines a single transformation
                                rule
                              properties:
                                action:
                                  type: string
                                fields:
                                  additionalProperties:
                                    type: string
                                  type: object
                                headerName:
                                  type: string
                                headerValue:
                                  type: string
                                headers:
                                  items:
                                    type: string
                                  type: array
                                type:
                                  type: string
                              required:
                              - action
                              - type
                              type: object
                            type: array
                          responseTransformations:
                            items:
                              description: TransformationRule defines a single transformation
                                rule
                              properties:
                                action:
                                  type: string
                                fields:
                                  additionalProperties:
                                    type: string
                                  type: object
                                headerName:
                                  type: string
                                headerValue:
                                  type: string
                                headers:
                                  items:
                                    type: string
                                  type: array
                                type:
                                  type: string
                              required:
                              - action
                              - type
                              type: object
                            type: array
                        type: object
                      rateLimit:
                        description: Rate limiting configuration
                        properties:
                          requests:
                            format: int64
                            type: integer
                          window:
                            type: string
                        required:
                        - requests
                        - window
                        type: object
                      security:
                        description: Security policies
                        properties:
                          allowedIPs:
                            items:
                              type: string
                            type: array
                          blockedIPs:
                            items:
                              type: string
                            type: array
                          minTLSVersion:
                            type: string
                          requireTLS:
                            type: boolean
                        type: object
                    type: object
                type: object
            type: object
          status:
            description: APIClassStatus defines the observed state of APIClass.
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
