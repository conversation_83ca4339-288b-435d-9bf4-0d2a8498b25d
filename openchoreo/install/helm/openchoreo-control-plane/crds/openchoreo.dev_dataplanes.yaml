apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: dataplanes.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: DataPlane
    listKind: DataPlaneList
    plural: dataplanes
    shortNames:
    - dp
    - dps
    singular: dataplane
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: DataPlane is the Schema for the dataplanes API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: DataPlaneSpec defines the desired state of a DataPlane.
            properties:
              gateway:
                description: Gateway specifies the configuration for the API gateway
                  in this DataPlane.
                properties:
                  organizationVirtualHost:
                    description: Organization-specific virtual host for the gateway
                    type: string
                  publicVirtualHost:
                    description: Public virtual host for the gateway
                    type: string
                required:
                - organizationVirtualHost
                - publicVirtualHost
                type: object
              kubernetesCluster:
                description: KubernetesCluster defines the target Kubernetes cluster
                  where workloads should be deployed.
                properties:
                  credentials:
                    description: Credentials contains the authentication details for
                      accessing the Kubernetes API server.
                    properties:
                      apiServerURL:
                        description: APIServerURL is the URL of the Kubernetes API
                          server.
                        type: string
                      caCert:
                        description: CACert is the base64-encoded CA certificate used
                          to verify the server's certificate.
                        type: string
                      clientCert:
                        description: ClientCert is the base64-encoded client certificate
                          used for authentication.
                        type: string
                      clientKey:
                        description: ClientKey is the base64-encoded private key corresponding
                          to the client certificate.
                        type: string
                    required:
                    - apiServerURL
                    - caCert
                    - clientCert
                    - clientKey
                    type: object
                  name:
                    description: Name of the Kubernetes cluster
                    type: string
                required:
                - credentials
                - name
                type: object
              observer:
                description: Observer specifies the configuration for the Observer
                  API integration.
                properties:
                  authentication:
                    description: Authentication contains the authentication configuration
                    properties:
                      basicAuth:
                        description: BasicAuth contains basic authentication credentials
                        properties:
                          password:
                            description: Password for basic authentication
                            type: string
                          username:
                            description: Username for basic authentication
                            type: string
                        required:
                        - password
                        - username
                        type: object
                    required:
                    - basicAuth
                    type: object
                  url:
                    description: URL is the base URL of the Observer API
                    type: string
                required:
                - authentication
                - url
                type: object
              registry:
                description: Registry contains the configuration required to pull
                  images from a container registry.
                properties:
                  prefix:
                    description: Prefix specifies the registry domain and namespace
                      (e.g., docker.io/namespace) that this configuration applies
                      to.
                    type: string
                  secretRef:
                    description: |-
                      SecretRef is the name of the Kubernetes Secret containing credentials for accessing the registry.
                      This field is optional and can be omitted for public or unauthenticated registries.
                    type: string
                required:
                - prefix
                type: object
            required:
            - gateway
            - kubernetesCluster
            - registry
            type: object
          status:
            description: DataPlaneStatus defines the observed state of DataPlane.
            properties:
              conditions:
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                description: |-
                  INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
                  Important: Run "make" to regenerate code after modifying this file
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
