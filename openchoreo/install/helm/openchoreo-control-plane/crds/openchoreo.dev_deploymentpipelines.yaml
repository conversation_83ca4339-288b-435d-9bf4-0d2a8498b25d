apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.16.4
  name: deploymentpipelines.openchoreo.dev
spec:
  group: openchoreo.dev
  names:
    kind: DeploymentPipeline
    listKind: DeploymentPipelineList
    plural: deploymentpipelines
    shortNames:
    - deppipe
    - deppipes
    singular: deploymentpipeline
  scope: Namespaced
  versions:
  - name: v1alpha1
    schema:
      openAPIV3Schema:
        description: DeploymentPipeline is the Schema for the deploymentpipelines
          API.
        properties:
          apiVersion:
            description: |-
              APIVersion defines the versioned schema of this representation of an object.
              Servers should convert recognized schemas to the latest internal value, and
              may reject unrecognized values.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources
            type: string
          kind:
            description: |-
              Kind is a string value representing the REST resource this object represents.
              Servers may infer this from the endpoint the client submits requests to.
              Cannot be updated.
              In CamelCase.
              More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds
            type: string
          metadata:
            type: object
          spec:
            description: DeploymentPipelineSpec defines the desired state of DeploymentPipeline.
            properties:
              promotionPaths:
                description: PromotionPaths defines the available paths for promotion
                  between environments
                items:
                  description: PromotionPath defines a path for promoting between
                    environments
                  properties:
                    sourceEnvironmentRef:
                      description: SourceEnvironmentRef is the reference to the source
                        environment
                      type: string
                    targetEnvironmentRefs:
                      description: TargetEnvironmentRefs is the list of target environments
                        and their approval requirements
                      items:
                        description: TargetEnvironmentRef defines a reference to a
                          target environment with approval settings
                        properties:
                          isManualApprovalRequired:
                            description: IsManualApprovalRequired indicates if manual
                              approval is needed for promotion
                            type: boolean
                          name:
                            description: Name of the target environment
                            type: string
                          requiresApproval:
                            description: RequiresApproval indicates if promotion to
                              this environment requires approval
                            type: boolean
                        required:
                        - name
                        type: object
                      type: array
                  required:
                  - sourceEnvironmentRef
                  - targetEnvironmentRefs
                  type: object
                type: array
            type: object
          status:
            description: DeploymentPipelineStatus defines the observed state of DeploymentPipeline.
            properties:
              conditions:
                description: Conditions represent the latest available observations
                  of an object's state
                items:
                  description: Condition contains details for one aspect of the current
                    state of this API Resource.
                  properties:
                    lastTransitionTime:
                      description: |-
                        lastTransitionTime is the last time the condition transitioned from one status to another.
                        This should be when the underlying condition changed.  If that is not known, then using the time when the API field changed is acceptable.
                      format: date-time
                      type: string
                    message:
                      description: |-
                        message is a human readable message indicating details about the transition.
                        This may be an empty string.
                      maxLength: 32768
                      type: string
                    observedGeneration:
                      description: |-
                        observedGeneration represents the .metadata.generation that the condition was set based upon.
                        For instance, if .metadata.generation is currently 12, but the .status.conditions[x].observedGeneration is 9, the condition is out of date
                        with respect to the current state of the instance.
                      format: int64
                      minimum: 0
                      type: integer
                    reason:
                      description: |-
                        reason contains a programmatic identifier indicating the reason for the condition's last transition.
                        Producers of specific condition types may define expected values and meanings for this field,
                        and whether the values are considered a guaranteed API.
                        The value should be a CamelCase string.
                        This field may not be empty.
                      maxLength: 1024
                      minLength: 1
                      pattern: ^[A-Za-z]([A-Za-z0-9_,:]*[A-Za-z0-9_])?$
                      type: string
                    status:
                      description: status of the condition, one of True, False, Unknown.
                      enum:
                      - "True"
                      - "False"
                      - Unknown
                      type: string
                    type:
                      description: type of condition in CamelCase or in foo.example.com/CamelCase.
                      maxLength: 316
                      pattern: ^([a-z0-9]([-a-z0-9]*[a-z0-9])?(\.[a-z0-9]([-a-z0-9]*[a-z0-9])?)*/)?(([A-Za-z0-9][-A-Za-z0-9_.]*)?[A-Za-z0-9])$
                      type: string
                  required:
                  - lastTransitionTime
                  - message
                  - reason
                  - status
                  - type
                  type: object
                type: array
              observedGeneration:
                description: ObservedGeneration represents the .metadata.generation
                  that the condition was set based upon
                format: int64
                type: integer
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
