{{ if .Values.global.defaultResources.enabled }}
{{- range .Values.global.defaultResources.environments }}
---
apiVersion: openchoreo.dev/v1alpha1
kind: Environment
metadata:
  name: {{ .name }}
  namespace: default
  annotations:
    openchoreo.dev/display-name: {{ .displayName }}
    openchoreo.dev/description: {{ .displayName }}
  labels:
    openchoreo.dev/organization: default
    openchoreo.dev/name: {{ .name }}
spec:
  dataPlaneRef: default #TODO: Use kubebulder defaults to set this
  isProduction: {{ .isCritical }}
  gateway:
    dnsPrefix: {{ .dnsPrefix }}
{{- end }}
{{ end }}
