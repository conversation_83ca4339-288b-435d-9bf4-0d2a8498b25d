{{ if .Values.global.defaultResources.enabled }}
apiVersion: openchoreo.dev/v1alpha1
kind: DeploymentPipeline
metadata:
  name: default
  namespace: default
  annotations:
    openchoreo.dev/display-name: {{ .Values.global.defaultResources.deploymentPipeline.displayName }}
    openchoreo.dev/description: {{ .Values.global.defaultResources.deploymentPipeline.description }}
  labels:
    openchoreo.dev/organization: default
    openchoreo.dev/name: default
spec:
  promotionPaths:
  {{- toYaml .Values.global.defaultResources.deploymentPipeline.promotionOrder | nindent 4 }}
{{ end }}
