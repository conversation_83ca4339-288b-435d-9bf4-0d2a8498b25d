{{ if .Values.global.defaultResources.enabled }}
apiVersion: batch/v1
kind: Job
metadata:
  name: wait-for-controller-ready
  annotations:
    "helm.sh/hook": post-install, post-upgrade
    "helm.sh/hook-weight": "6"
    "helm.sh/hook-delete-policy": hook-succeeded,hook-failed
spec:
  template:
    spec:
      serviceAccountName: controller-wait-sa
      containers:
        - name: wait-for-ready
          image: {{ .Values.waitJob.image }}
          imagePullPolicy: IfNotPresent
          command:
            - /bin/sh
            - -c
            - |
              echo "Waiting for openchoreo controller to be ready..."
              kubectl wait --namespace {{ .Release.Namespace }} \
              --for=condition=Ready pod \
              -l app.kubernetes.io/name=openchoreo-control-plane,app.kubernetes.io/component=controller-manager \
              --timeout=900s
      restartPolicy: Never
  backoffLimit: 5
{{ end }}
