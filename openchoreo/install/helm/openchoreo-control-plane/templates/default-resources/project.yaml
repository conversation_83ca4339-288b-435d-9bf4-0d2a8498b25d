{{ if .Values.global.defaultResources.enabled }}
apiVersion: openchoreo.dev/v1alpha1
kind: Project
metadata:
  name: default
  namespace: default
  annotations:
    openchoreo.dev/display-name: {{ .Values.global.defaultResources.project.displayName }}
    openchoreo.dev/description: {{ .Values.global.defaultResources.project.description }}
  labels:
    openchoreo.dev/organization: default
    openchoreo.dev/name: default
spec:
  deploymentPipelineRef: default
{{ end }}
