apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.controllerManager.name }}-webhook-service
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
spec:
  type: {{ .Values.webhookService.type }}
  selector:
    {{- include "openchoreo-control-plane.componentSelectorLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
  ports:
  {{- .Values.webhookService.ports | toYaml | nindent 2 }}
