apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ .Values.controllerManager.name }}-leader-election
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ .Values.controllerManager.name }}-leader-election
subjects:
- kind: ServiceAccount
  name: {{ .Values.controllerManager.name }}
  namespace: {{ .Release.Namespace }}
