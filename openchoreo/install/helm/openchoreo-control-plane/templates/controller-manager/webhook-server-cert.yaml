apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: {{ .Values.controllerManager.name }}-webhook-server-cert
  namespace: {{ .Release.Namespace }}
  annotations:
    "helm.sh/hook": post-install,post-upgrade
    "helm.sh/hook-weight": "3"
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
spec:
  dnsNames:
    - '{{ .Values.controllerManager.name }}-webhook-service.{{ .Release.Namespace }}.svc'
    - '{{ .Values.controllerManager.name }}-webhook-service.{{ .Release.Namespace }}.svc.{{ .Values.kubernetesClusterDomain }}'
  issuerRef:
    kind: Issuer
    name: '{{ .Values.controllerManager.name }}-selfsigned-issuer'
  secretName: {{ .Values.controllerManager.name }}-webhook-server-cert
