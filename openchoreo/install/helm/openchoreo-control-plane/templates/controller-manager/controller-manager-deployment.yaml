apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.controllerManager.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
spec:
  replicas: {{ .Values.controllerManager.replicas }}
  selector:
    matchLabels:
      {{- include "openchoreo-control-plane.componentSelectorLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "openchoreo-control-plane.componentSelectorLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 8 }}
      annotations:
        kubectl.kubernetes.io/default-container: manager
    spec:
      containers:
      - args: {{- toYaml .Values.controllerManager.manager.args | nindent 8 }}
        command:
        - /manager
        env:
        - name: ENABLE_WEBHOOKS
          value: {{ quote .Values.controllerManager.manager.env.enableWebhooks }}
        - name: KUBERNETES_CLUSTER_DOMAIN
          value: {{ quote .Values.kubernetesClusterDomain }}
        image: {{ .Values.controllerManager.manager.image.repository }}:{{ .Values.controllerManager.manager.image.tag
          | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.controllerManager.manager.imagePullPolicy }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: 8081
          initialDelaySeconds: 15
          periodSeconds: 20
        name: manager
        ports:
        - containerPort: 9443
          name: webhook-server
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /readyz
            port: 8081
          initialDelaySeconds: 5
          periodSeconds: 10
        resources: {{- toYaml .Values.controllerManager.manager.resources | nindent 10
          }}
        securityContext: {{- toYaml .Values.controllerManager.manager.containerSecurityContext
          | nindent 10 }}
        volumeMounts:
        - mountPath: /tmp/k8s-webhook-server/serving-certs
          name: cert
          readOnly: true
      securityContext: {{- toYaml .Values.controllerManager.podSecurityContext | nindent
        8 }}
      serviceAccountName: {{ .Values.controllerManager.name }}
      terminationGracePeriodSeconds: 10
      volumes:
      - name: cert
        secret:
          defaultMode: 420
          secretName: {{ .Values.controllerManager.name }}-webhook-server-cert
