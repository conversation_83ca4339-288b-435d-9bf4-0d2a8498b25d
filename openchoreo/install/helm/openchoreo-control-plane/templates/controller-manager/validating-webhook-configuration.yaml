apiVersion: admissionregistration.k8s.io/v1
kind: ValidatingWebhookConfiguration
metadata:
  name: {{ include "openchoreo-control-plane.name" . }}-validating-webhook-configuration
  annotations:
    cert-manager.io/inject-ca-from: {{ .Release.Namespace }}/{{ .Values.controllerManager.name }}-webhook-server-cert
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.controllerManager.name) | nindent 4 }}
webhooks:
- admissionReviewVersions:
  - v1
  clientConfig:
    service:
      name: {{ .Values.controllerManager.name }}-webhook-service
      namespace: '{{ .Release.Namespace }}'
      path: /validate-openchoreo-dev-v1alpha1-project
  failurePolicy: Ignore
  name: vproject-v1alpha1.kb.io
  rules:
  - apiGroups:
    - openchoreo.dev
    apiVersions:
    - v1alpha1
    operations:
    - CREATE
    - UPDATE
    resources:
    - projects
  sideEffects: None
