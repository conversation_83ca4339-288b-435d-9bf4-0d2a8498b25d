apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.openchoreoApi.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.openchoreoApi.name) | nindent 4 }}
spec:
  selector:
    {{- include "openchoreo-control-plane.componentSelectorLabels" (dict "context" . "component" .Values.openchoreoApi.name) | nindent 4 }}
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 8080
  type: ClusterIP
