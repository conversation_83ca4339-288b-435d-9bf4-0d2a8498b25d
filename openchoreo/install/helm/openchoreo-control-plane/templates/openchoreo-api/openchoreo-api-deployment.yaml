apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.openchoreoApi.name }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "openchoreo-control-plane.componentLabels" (dict "context" . "component" .Values.openchoreoApi.name) | nindent 4 }}
spec:
  replicas: {{ .Values.openchoreoApi.replicas }}
  selector:
    matchLabels:
      {{- include "openchoreo-control-plane.componentSelectorLabels" (dict "context" . "component" .Values.openchoreoApi.name) | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "openchoreo-control-plane.componentSelectorLabels" (dict "context" . "component" .Values.openchoreoApi.name) | nindent 8 }}
    spec:
      serviceAccountName: {{ .Values.controllerManager.name }}
      containers:
      - name: openchoreo-api
        image: "{{ .Values.openchoreoApi.image }}:{{ .Values.openchoreoApi.tag }}"
        imagePullPolicy: {{ .Values.openchoreoApi.imagePullPolicy }}
        ports:
        - containerPort: 8080
        resources:
          requests:
            cpu: {{ .Values.openchoreoApi.resources.requests.cpu }}
            memory: {{ .Values.openchoreoApi.resources.requests.memory }}
          limits:
            cpu: {{ .Values.openchoreoApi.resources.limits.cpu }}
            memory: {{ .Values.openchoreoApi.resources.limits.memory }}
